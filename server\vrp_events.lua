-- vRP Specific Events for CB Pause Menu

-- Event: Get player inventory
RegisterServerEvent('cb_pausemenu:getInventory')
AddEventHandler('cb_pausemenu:getInventory', function()
    local source = source
    local user_id = vRP.getUserId({source})
    
    if user_id then
        local inventory = vRP.getInventory({user_id}) or {}
        local formattedInventory = {}
        
        for item, data in pairs(inventory) do
            if data.amount and data.amount > 0 then
                table.insert(formattedInventory, {
                    name = item,
                    label = vRP.getItemName({item}) or item,
                    count = data.amount,
                    weight = vRP.getItemWeight({item}) or 0
                })
            end
        end
        
        TriggerClientEvent('cb_pausemenu:updateInventory', source, formattedInventory)
    end
end)

-- Event: Get player vehicles
RegisterServerEvent('cb_pausemenu:getVehicles')
AddEventHandler('cb_pausemenu:getVehicles', function()
    local source = source
    local user_id = vRP.getUserId({source})
    
    if user_id then
        local vehicles = vRP.getUserVehicles({user_id}) or {}
        local formattedVehicles = {}
        
        for vehicle, data in pairs(vehicles) do
            table.insert(formattedVehicles, {
                model = vehicle,
                name = vehicle,
                plate = data.plate or "UNKNOWN",
                stored = data.stored or 1,
                garage = data.garage or "default"
            })
        end
        
        TriggerClientEvent('cb_pausemenu:updateVehicles', source, formattedVehicles)
    end
end)

-- Event: Get player homes/apartments
RegisterServerEvent('cb_pausemenu:getHomes')
AddEventHandler('cb_pausemenu:getHomes', function()
    local source = source
    local user_id = vRP.getUserId({source})
    
    if user_id then
        local homes = vRP.getUserHomes({user_id}) or {}
        local formattedHomes = {}
        
        for home, data in pairs(homes) do
            table.insert(formattedHomes, {
                name = home,
                number = data.number or 0,
                x = data.x or 0,
                y = data.y or 0,
                z = data.z or 0
            })
        end
        
        TriggerClientEvent('cb_pausemenu:updateHomes', source, formattedHomes)
    end
end)

-- Event: Give money to player (admin only)
RegisterServerEvent('cb_pausemenu:giveMoney')
AddEventHandler('cb_pausemenu:giveMoney', function(targetId, amount, account)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            if account == "bank" then
                vRP.giveBankMoney({target_id, amount})
            else
                vRP.giveMoney({target_id, amount})
            end
            
            TriggerClientEvent('cb_pausemenu:notify', source, 'تم إعطاء المال للاعب', 'success')
            TriggerClientEvent('cb_pausemenu:notify', targetId, 'تم إعطاؤك ' .. amount .. '$', 'success')
            
            -- Log action
            LogPlayerAction(source, 'money_given', {
                target_id = target_id,
                amount = amount,
                account = account
            })
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Remove money from player (admin only)
RegisterServerEvent('cb_pausemenu:removeMoney')
AddEventHandler('cb_pausemenu:removeMoney', function(targetId, amount, account)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            local success = false
            
            if account == "bank" then
                if vRP.getBankMoney({target_id}) >= amount then
                    vRP.setBankMoney({target_id, vRP.getBankMoney({target_id}) - amount})
                    success = true
                end
            else
                if vRP.getMoney({target_id}) >= amount then
                    vRP.setMoney({target_id, vRP.getMoney({target_id}) - amount})
                    success = true
                end
            end
            
            if success then
                TriggerClientEvent('cb_pausemenu:notify', source, 'تم سحب المال من اللاعب', 'success')
                TriggerClientEvent('cb_pausemenu:notify', targetId, 'تم سحب ' .. amount .. '$ منك', 'warning')
                
                -- Log action
                LogPlayerAction(source, 'money_removed', {
                    target_id = target_id,
                    amount = amount,
                    account = account
                })
            else
                TriggerClientEvent('cb_pausemenu:notify', source, 'اللاعب لا يملك مال كافي', 'error')
            end
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Give item to player (admin only)
RegisterServerEvent('cb_pausemenu:giveItem')
AddEventHandler('cb_pausemenu:giveItem', function(targetId, item, amount)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            if vRP.giveInventoryItem({target_id, item, amount, true}) then
                TriggerClientEvent('cb_pausemenu:notify', source, 'تم إعطاء العنصر للاعب', 'success')
                TriggerClientEvent('cb_pausemenu:notify', targetId, 'تم إعطاؤك ' .. amount .. 'x ' .. item, 'success')
                
                -- Log action
                LogPlayerAction(source, 'item_given', {
                    target_id = target_id,
                    item = item,
                    amount = amount
                })
            else
                TriggerClientEvent('cb_pausemenu:notify', source, 'فشل في إعطاء العنصر', 'error')
            end
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Teleport to player (admin only)
RegisterServerEvent('cb_pausemenu:teleportToPlayer')
AddEventHandler('cb_pausemenu:teleportToPlayer', function(targetId)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            vRPclient.getPosition(targetId, {}, function(x, y, z)
                if x and y and z then
                    vRPclient.teleport(source, {x, y, z})
                    TriggerClientEvent('cb_pausemenu:notify', source, 'تم النقل إلى اللاعب', 'success')
                    
                    -- Log action
                    LogPlayerAction(source, 'teleport_to_player', {
                        target_id = target_id,
                        coords = {x = x, y = y, z = z}
                    })
                else
                    TriggerClientEvent('cb_pausemenu:notify', source, 'فشل في الحصول على موقع اللاعب', 'error')
                end
            end)
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Bring player to admin (admin only)
RegisterServerEvent('cb_pausemenu:bringPlayer')
AddEventHandler('cb_pausemenu:bringPlayer', function(targetId)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            vRPclient.getPosition(source, {}, function(x, y, z)
                if x and y and z then
                    vRPclient.teleport(targetId, {x, y, z})
                    TriggerClientEvent('cb_pausemenu:notify', source, 'تم جلب اللاعب إليك', 'success')
                    TriggerClientEvent('cb_pausemenu:notify', targetId, 'تم نقلك إلى الإدارة', 'info')
                    
                    -- Log action
                    LogPlayerAction(source, 'bring_player', {
                        target_id = target_id,
                        coords = {x = x, y = y, z = z}
                    })
                else
                    TriggerClientEvent('cb_pausemenu:notify', source, 'فشل في الحصول على موقعك', 'error')
                end
            end)
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Freeze player (admin only)
RegisterServerEvent('cb_pausemenu:freezePlayer')
AddEventHandler('cb_pausemenu:freezePlayer', function(targetId, freeze)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            vRPclient.setFrozen(targetId, {freeze})
            
            local action = freeze and 'تم تجميد اللاعب' or 'تم إلغاء تجميد اللاعب'
            TriggerClientEvent('cb_pausemenu:notify', source, action, 'success')
            
            local playerAction = freeze and 'تم تجميدك' or 'تم إلغاء تجميدك'
            TriggerClientEvent('cb_pausemenu:notify', targetId, playerAction, 'warning')
            
            -- Log action
            LogPlayerAction(source, freeze and 'player_frozen' or 'player_unfrozen', {
                target_id = target_id
            })
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Heal player (admin only)
RegisterServerEvent('cb_pausemenu:healPlayer')
AddEventHandler('cb_pausemenu:healPlayer', function(targetId)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            vRPclient.setHealth(targetId, {200})
            
            TriggerClientEvent('cb_pausemenu:notify', source, 'تم شفاء اللاعب', 'success')
            TriggerClientEvent('cb_pausemenu:notify', targetId, 'تم شفاؤك', 'success')
            
            -- Log action
            LogPlayerAction(source, 'player_healed', {
                target_id = target_id
            })
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Revive player (admin only)
RegisterServerEvent('cb_pausemenu:revivePlayer')
AddEventHandler('cb_pausemenu:revivePlayer', function(targetId)
    local source = source
    local admin_id = vRP.getUserId({source})
    local target_id = vRP.getUserId({targetId})
    
    if admin_id and target_id then
        if vRP.hasGroup({admin_id, "admin"}) or vRP.hasGroup({admin_id, "superadmin"}) then
            vRPclient.varyHealth(targetId, {200})
            
            TriggerClientEvent('cb_pausemenu:notify', source, 'تم إحياء اللاعب', 'success')
            TriggerClientEvent('cb_pausemenu:notify', targetId, 'تم إحياؤك', 'success')
            
            -- Log action
            LogPlayerAction(source, 'player_revived', {
                target_id = target_id
            })
        else
            TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        end
    end
end)

-- Event: Send notification to player
RegisterServerEvent('cb_pausemenu:notify')
AddEventHandler('cb_pausemenu:notify', function(message, type)
    local source = source
    local user_id = vRP.getUserId({source})
    
    if user_id then
        vRPclient.notify(source, {message})
    end
end)
