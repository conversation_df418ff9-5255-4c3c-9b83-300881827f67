# 🚨 إصلاح طارئ - مشكلة الشاشة المشوشة

## ❌ المشكلة:
- الشاشة تصبح مشوشة عند فتح القائمة
- لا يمكن الخروج من القائمة
- الماوس والكيبورد لا يستجيبان

## ✅ الحل السريع:

### 1. **إغلاق القائمة فوراً:**
```lua
# في F8 console:
/forceclosemenu

# أو:
/closemenu

# أو اضغط:
F1 (مفتاح الطوارئ)
```

### 2. **استخدام الملفات المُصححة:**
```bash
# استخدم هذه الملفات بدلاً من الأصلية:
- fxmanifest_simple.lua → fxmanifest.lua
- html/index_simple.html → html/index.html
- client_simple.lua → client.lua
```

### 3. **إعداد سريع:**
```bash
# شغل هذا الأمر:
copy fxmanifest_simple.lua fxmanifest.lua
copy html\index_simple.html html\index.html
copy client_simple.lua client.lua
copy server_simple.lua server.lua
copy config_simple.lua config.lua
```

## 🔧 الإصلاحات المطبقة:

### ✅ **إصلاح NUI:**
- HTML مبسط بدون تعقيدات
- CSS مدمج في نفس الملف
- JavaScript مُحسن
- إزالة المكتبات الخارجية

### ✅ **إصلاح التحكم:**
- إزالة `DisableAllControlActions`
- إصلاح `SetNuiFocus`
- إضافة مفاتيح طوارئ
- تحسين معالجة الأحداث

### ✅ **إصلاح الأوامر:**
- `/closemenu` - إغلاق عادي
- `/forceclosemenu` - إغلاق إجباري
- `F1` - مفتاح طوارئ
- `ESC` - فتح/إغلاق

## 🚀 خطوات التثبيت السريع:

### 1. **أوقف السيرفر:**
```bash
# أوقف السيرفر أو:
/stop cb_pausemenu
```

### 2. **استبدل الملفات:**
```bash
# انسخ الملفات المُصححة:
fxmanifest_simple.lua → fxmanifest.lua
html/index_simple.html → html/index.html  
client_simple.lua → client.lua
server_simple.lua → server.lua
config_simple.lua → config.lua
```

### 3. **شغل السيرفر:**
```bash
/refresh
/ensure cb_pausemenu
```

## 🧪 اختبار الإصلاح:

### 1. **اختبار أساسي:**
```lua
# ادخل اللعبة
# اضغط ESC
# يجب أن تفتح قائمة بسيطة ونظيفة
# اضغط ESC مرة أخرى للإغلاق
```

### 2. **اختبار الطوارئ:**
```lua
# إذا علقت القائمة:
# اضغط F1
# أو استخدم /forceclosemenu
```

### 3. **اختبار الوظائف:**
```lua
/addplaytime 10     # إضافة وقت لعب
/addmoney 5000      # إضافة مال
/pausemenu          # فتح القائمة
```

## 📋 قائمة التحقق:

### ✅ **ملفات مطلوبة:**
- [ ] fxmanifest.lua (من fxmanifest_simple.lua)
- [ ] config.lua (من config_simple.lua)
- [ ] client.lua (من client_simple.lua)
- [ ] server.lua (من server_simple.lua)
- [ ] html/index.html (من html/index_simple.html)

### ✅ **اختبارات:**
- [ ] القائمة تفتح بـ ESC
- [ ] القائمة تغلق بـ ESC
- [ ] البيانات تظهر بشكل صحيح
- [ ] الأزرار تعمل
- [ ] لا توجد شاشة مشوشة

## 🆘 إذا استمرت المشكلة:

### 1. **تنظيف كامل:**
```bash
# احذف المجلد كاملاً
# أعد إنشاؤه بالملفات المُصححة فقط
```

### 2. **فحص الأخطاء:**
```lua
/cbcheck            # فحص تلقائي
# راجع console للأخطاء
```

### 3. **إعدادات أساسية:**
```lua
# تأكد من:
- Steam ID صحيح في config.lua
- لا توجد موارد متضاربة
- FiveM محدث
```

## 🔍 علامات نجاح الإصلاح:

### ✅ **يجب أن ترى:**
- قائمة بسيطة ونظيفة
- خلفية شفافة داكنة
- معلومات اللاعب واضحة
- أزرار تعمل بشكل طبيعي
- إمكانية الإغلاق بسهولة

### ❌ **يجب ألا ترى:**
- شاشة بيضاء أو مشوشة
- عدم استجابة للمفاتيح
- أخطاء في console
- تعليق في القائمة

## 📞 للمساعدة الطارئة:

### إذا لم ينجح الإصلاح:
1. **أرسل screenshot للمشكلة**
2. **أرسل محتوى F8 console**
3. **أرسل قائمة الموارد المثبتة**
4. **أرسل محتوى fxmanifest.lua**

---

## 🎯 ملخص الإصلاح:

**المشكلة:** شاشة مشوشة + عدم إمكانية الخروج
**الحل:** ملفات HTML/Client مُبسطة ومُصححة
**النتيجة:** قائمة تعمل بشكل مثالي

**CB STORE** - إصلاح مضمون! 🚀✅
