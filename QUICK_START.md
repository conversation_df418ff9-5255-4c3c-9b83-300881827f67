# CB Pause Menu - دليل البدء السريع

## 🚀 للاختبار السريع (بدون vRP)

### 1. الملفات المطلوبة للاختبار:
```
cb_pausemenu/
├── fxmanifest_simple.lua (أعد تسميته إلى fxmanifest.lua)
├── config_simple.lua (أعد تسميته إلى config.lua)
├── client_simple.lua (أعد تسميته إلى client.lua)
├── server_simple.lua (أعد تسميته إلى server.lua)
└── html/ (جميع ملفات الواجهة)
```

### 2. خطوات التثبيت السريع:

#### أ) إعداد الملفات:
```bash
# 1. أعد تسمية الملفات
mv fxmanifest_simple.lua fxmanifest.lua
mv config_simple.lua config.lua
mv client_simple.lua client.lua
mv server_simple.lua server.lua

# 2. أو استخدم الملفات المبسطة فقط
```

#### ب) إضافة للسيرفر:
```lua
# في server.cfg
ensure cb_pausemenu
```

#### ج) تخصيص الإعدادات:
```lua
# في config.lua - أضف Steam ID الخاص بك للإدارة
Config.Admins = {
    "steam:YOUR_STEAM_ID_HERE",
    "license:YOUR_LICENSE_HERE"
}
```

### 3. الاختبار:

#### أ) فتح القائمة:
- اضغط `ESC` في اللعبة
- أو استخدم الأمر `/pausemenu`

#### ب) أوامر الاختبار:
```lua
/addplaytime 10        # إضافة 10 ساعات لوقت اللعب
/addmoney 5000         # إضافة $5000
/cbadmin               # التحقق من صلاحيات الإدارة
/setmoney [ID] [AMOUNT] # تعديل مال لاعب (للإدارة)
```

### 4. المميزات المتاحة في النسخة المبسطة:

#### ✅ يعمل:
- فتح/إغلاق القائمة
- عرض معلومات اللاعب الأساسية
- عداد FPS
- النقل للنقطة المحددة
- قطع الاتصال
- أوامر الإدارة البسيطة
- تتبع وقت اللعب
- نظام الرتب

#### ❌ غير متوفر (في النسخة المبسطة):
- تكامل vRP
- قاعدة البيانات
- Discord Integration
- نظام الأصدقاء المتقدم
- الحقيبة والمركبات

### 5. استكشاف الأخطاء:

#### أ) القائمة لا تفتح:
```lua
# تحقق من console للأخطاء
# تأكد من وجود ملفات html/
# تحقق من أن المورد يعمل: /refresh && /ensure cb_pausemenu
```

#### ب) البيانات لا تظهر:
```lua
# تحقق من console
# استخدم /addplaytime و /addmoney للاختبار
# تأكد من أن config.lua محمل بشكل صحيح
```

#### ج) مشاكل الصلاحيات:
```lua
# أضف Steam ID الصحيح في config.lua
# استخدم /cbadmin للتحقق
# تحقق من console للأخطاء
```

### 6. الحصول على Steam ID:

#### أ) في اللعبة:
```lua
# استخدم F8 console واكتب:
print(GetPlayerIdentifier(PlayerId(), 0))
```

#### ب) من المواقع:
- steamid.io
- steamidfinder.com

### 7. التطوير والتخصيص:

#### أ) إضافة مميزات:
```lua
# عدل client_simple.lua للعميل
# عدل server_simple.lua للخادم
# عدل config_simple.lua للإعدادات
```

#### ب) تخصيص الواجهة:
```css
# عدل html/style.css للتصميم
# عدل html/script.js للوظائف
# عدل html/index.html للهيكل
```

### 8. الترقية لـ vRP:

عندما تريد استخدام vRP:
```lua
# 1. استخدم الملفات الأصلية:
#    - fxmanifest.lua
#    - config.lua  
#    - client/main.lua
#    - server/main.lua

# 2. تأكد من تحميل vRP أولاً
# 3. شغل database.sql
# 4. عدل الإعدادات في config.lua
```

### 9. الدعم:

#### أ) مشاكل شائعة:
- تأكد من تحميل المورد بعد restart
- تحقق من وجود جميع ملفات html/
- تأكد من صحة Steam ID في config

#### ب) للمساعدة:
- تحقق من console للأخطاء
- استخدم أوامر الاختبار
- راجع ملف config.lua

---

## 🎮 نصائح للاختبار:

1. **ابدأ بالنسخة المبسطة** للتأكد من عمل الأساسيات
2. **استخدم أوامر الاختبار** لإضافة البيانات
3. **تحقق من console** لأي أخطاء
4. **اختبر جميع الأقسام** في القائمة
5. **تأكد من الصلاحيات** قبل اختبار أوامر الإدارة

**CB STORE** - نسخة مبسطة للاختبار السريع! 🚀
