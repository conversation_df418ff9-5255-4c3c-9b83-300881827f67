# CB Pause Menu - Server Configuration Example
# Copy this file to your server.cfg and modify as needed

# Basic Server Settings
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"

# Server Information
sv_hostname "CB STORE SERVER - Advanced Pause Menu"
sv_maxclients 64
sv_endpointprivacy true

# License Key (Get from https://keymaster.fivem.net)
sv_licenseKey "YOUR_LICENSE_KEY_HERE"

# Steam Web API Key (Optional, for Steam integration)
steam_webApiKey "YOUR_STEAM_API_KEY"

# Database Configuration
set mysql_connection_string "mysql://username:password@localhost/database_name?charset=utf8mb4"

# CB Pause Menu Configuration
set cb_pausemenu_debug "false"
set cb_pausemenu_locale "ar"
set cb_pausemenu_discord_token "YOUR_DISCORD_BOT_TOKEN"
set cb_pausemenu_discord_webhook "YOUR_DISCORD_WEBHOOK_URL"
set cb_pausemenu_enable_achievements "true"
set cb_pausemenu_enable_friends "true"
set cb_pausemenu_enable_vip "true"
set cb_pausemenu_auto_ban_reports "5"
set cb_pausemenu_report_cooldown "300"

# Required Resources
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

# Framework (Choose one)
ensure es_extended
# ensure qb-core

# Database
ensure mysql-async
# ensure oxmysql

# CB Pause Menu
ensure cb_pausemenu

# Optional Resources for Enhanced Experience
ensure screenshot-basic  # For screenshot functionality
ensure discord-rich-presence  # For Discord integration

# Admin Resources (Optional)
# ensure esx_adminmenu
# ensure qb-admin

# Performance Settings
set onesync on
set onesync_enableInfinity 1
set onesync_enableBeyond 1
set onesync_population true

# Network Settings
set sv_enforceGameBuild 2699
set sv_sendLogToDiscord "true"
set sv_fxdkMode "false"

# Security Settings
set sv_scriptHookAllowed 0
set sv_useCEFAudioAPI true

# Logging
set sv_logFile "server.log"
set sv_logLevel "info"

# Tags for Server Browser
sets tags "cb-store,pause-menu,arabic,esx,roleplay"
sets locale "ar-SA"
sets banner_detail "https://your-server-banner-url.com/banner.png"
sets banner_connecting "https://your-server-banner-url.com/connecting.png"

# Loading Screen (Optional)
# loadscreen "https://your-loading-screen-url.com"

# Server Description
sets sv_projectName "CB STORE SERVER"
sets sv_projectDesc "Advanced FiveM Server with CB Pause Menu"

# Discord Integration
sets Discord "https://discord.gg/your-discord"
sets Website "https://your-website.com"

# Additional ConVars for CB Pause Menu
set cb_pausemenu_server_logo "https://your-server-logo-url.com/logo.png"
set cb_pausemenu_maintenance_mode "false"
set cb_pausemenu_max_playtime_hours "500"
set cb_pausemenu_enable_proxy "false"
set cb_pausemenu_enable_tunnel "false"
set cb_pausemenu_fps_update_interval "1000"
set cb_pausemenu_enable_blur "true"
set cb_pausemenu_enable_animations "true"

# Performance Monitoring
set cb_pausemenu_monitor_performance "true"
set cb_pausemenu_performance_threshold "30"  # Minimum FPS threshold

# Security Settings for CB Pause Menu
set cb_pausemenu_admin_only_commands "ban,kick,teleport,godmode,invisible"
set cb_pausemenu_enable_anticheat "true"
set cb_pausemenu_log_all_actions "true"

# Backup Settings
set cb_pausemenu_auto_backup "true"
set cb_pausemenu_backup_interval "3600"  # Backup every hour

# Rate Limiting
set cb_pausemenu_rate_limit_enabled "true"
set cb_pausemenu_max_requests_per_minute "60"

# File Upload Settings (for screenshots)
set cb_pausemenu_max_file_size "5242880"  # 5MB
set cb_pausemenu_allowed_file_types "png,jpg,jpeg"

# Webhook Settings
set cb_pausemenu_webhook_timeout "5000"
set cb_pausemenu_webhook_retry_attempts "3"

# Cache Settings
set cb_pausemenu_cache_enabled "true"
set cb_pausemenu_cache_duration "300"  # 5 minutes

# Debug Settings (Development Only)
set cb_pausemenu_debug_mode "false"
set cb_pausemenu_verbose_logging "false"
set cb_pausemenu_enable_profiler "false"

# Restart the server after configuration changes
# restart cb_pausemenu
