-- CB Pause Menu Database Schema
-- Execute this SQL to create the required database tables

-- Table for storing player data
CREATE TABLE IF NOT EXISTS `cb_pausemenu_data` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `data` longtext NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing player bans
CREATE TABLE IF NOT EXISTS `cb_pausemenu_bans` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `player_name` varchar(100) NOT NULL,
    `reason` text NOT NULL,
    `duration` int(11) NOT NULL DEFAULT 0 COMMENT '0 = permanent, otherwise seconds',
    `banned_by` varchar(100) NOT NULL,
    `timestamp` int(11) NOT NULL,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (`id`),
    KEY `identifier` (`identifier`),
    KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing action logs
CREATE TABLE IF NOT EXISTS `cb_pausemenu_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `player_name` varchar(100) NOT NULL,
    `action` varchar(50) NOT NULL,
    `details` longtext,
    `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `identifier` (`identifier`),
    KEY `action` (`action`),
    KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing bug reports
CREATE TABLE IF NOT EXISTS `cb_pausemenu_bug_reports` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `player_name` varchar(100) NOT NULL,
    `description` text NOT NULL,
    `category` varchar(50) NOT NULL,
    `status` enum('open','in_progress','resolved','closed') NOT NULL DEFAULT 'open',
    `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `resolved_at` timestamp NULL DEFAULT NULL,
    `resolved_by` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `identifier` (`identifier`),
    KEY `status` (`status`),
    KEY `category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing player reports
CREATE TABLE IF NOT EXISTS `cb_pausemenu_reports` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `reporter_id` varchar(50) NOT NULL,
    `reporter_name` varchar(100) NOT NULL,
    `target_id` int(11) NOT NULL,
    `target_name` varchar(100) NOT NULL,
    `reason` varchar(100) NOT NULL,
    `description` text NOT NULL,
    `status` enum('pending','reviewed','resolved','dismissed') NOT NULL DEFAULT 'pending',
    `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `reviewed_at` timestamp NULL DEFAULT NULL,
    `reviewed_by` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `reporter_id` (`reporter_id`),
    KEY `target_id` (`target_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing friend relationships
CREATE TABLE IF NOT EXISTS `cb_pausemenu_friends` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player1_id` varchar(50) NOT NULL,
    `player2_id` varchar(50) NOT NULL,
    `status` enum('pending','accepted','blocked') NOT NULL DEFAULT 'pending',
    `requested_by` varchar(50) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_friendship` (`player1_id`,`player2_id`),
    KEY `player1_id` (`player1_id`),
    KEY `player2_id` (`player2_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing achievements
CREATE TABLE IF NOT EXISTS `cb_pausemenu_achievements` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `achievement_id` varchar(100) NOT NULL,
    `unlocked_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `progress` int(11) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_achievement` (`identifier`,`achievement_id`),
    KEY `identifier` (`identifier`),
    KEY `achievement_id` (`achievement_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing VIP status
CREATE TABLE IF NOT EXISTS `cb_pausemenu_vip` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `level` int(11) NOT NULL DEFAULT 1,
    `expires_at` timestamp NULL DEFAULT NULL,
    `granted_by` varchar(100) NOT NULL,
    `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (`id`),
    UNIQUE KEY `identifier` (`identifier`),
    KEY `level` (`level`),
    KEY `active` (`active`),
    KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing server settings
CREATE TABLE IF NOT EXISTS `cb_pausemenu_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL,
    `setting_value` longtext NOT NULL,
    `description` text,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO `cb_pausemenu_settings` (`setting_key`, `setting_value`, `description`) VALUES
('maintenance_mode', 'false', 'Server maintenance mode status'),
('max_playtime_hours', '500', 'Maximum playtime hours for highest rank'),
('discord_webhook', '', 'Discord webhook URL for logging'),
('server_logo_url', '', 'Server logo URL'),
('enable_achievements', 'true', 'Enable achievements system'),
('enable_friends', 'true', 'Enable friends system'),
('enable_vip', 'true', 'Enable VIP system'),
('auto_ban_reports', '5', 'Auto ban after X reports'),
('report_cooldown', '300', 'Report cooldown in seconds');

-- Create indexes for better performance
CREATE INDEX `idx_cb_pausemenu_data_identifier` ON `cb_pausemenu_data` (`identifier`);
CREATE INDEX `idx_cb_pausemenu_logs_timestamp` ON `cb_pausemenu_logs` (`timestamp`);
CREATE INDEX `idx_cb_pausemenu_bans_timestamp` ON `cb_pausemenu_bans` (`timestamp`);
CREATE INDEX `idx_cb_pausemenu_reports_timestamp` ON `cb_pausemenu_reports` (`timestamp`);

-- Create views for easier data access
CREATE VIEW `v_active_bans` AS
SELECT * FROM `cb_pausemenu_bans` 
WHERE `active` = 1 AND (`duration` = 0 OR `timestamp` + `duration` > UNIX_TIMESTAMP());

CREATE VIEW `v_player_stats` AS
SELECT 
    `identifier`,
    JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.playtime')) as `playtime`,
    JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.rank')) as `rank`,
    JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.lastSeen')) as `last_seen`,
    `updated_at`
FROM `cb_pausemenu_data`;

-- Stored procedures for common operations
DELIMITER //

CREATE PROCEDURE `GetPlayerData`(IN `player_identifier` VARCHAR(50))
BEGIN
    SELECT * FROM `cb_pausemenu_data` WHERE `identifier` = player_identifier;
END //

CREATE PROCEDURE `UpdatePlayerPlaytime`(IN `player_identifier` VARCHAR(50), IN `new_playtime` DECIMAL(10,2))
BEGIN
    UPDATE `cb_pausemenu_data` 
    SET `data` = JSON_SET(`data`, '$.playtime', new_playtime)
    WHERE `identifier` = player_identifier;
END //

CREATE PROCEDURE `GetTopPlayers`(IN `limit_count` INT)
BEGIN
    SELECT 
        `identifier`,
        JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.playtime')) as `playtime`,
        JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.rank')) as `rank`
    FROM `cb_pausemenu_data`
    ORDER BY CAST(JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.playtime')) AS DECIMAL(10,2)) DESC
    LIMIT limit_count;
END //

DELIMITER ;

-- Create triggers for automatic cleanup
DELIMITER //

CREATE TRIGGER `cleanup_old_logs` 
AFTER INSERT ON `cb_pausemenu_logs`
FOR EACH ROW
BEGIN
    DELETE FROM `cb_pausemenu_logs` 
    WHERE `timestamp` < DATE_SUB(NOW(), INTERVAL 30 DAY);
END //

CREATE TRIGGER `update_ban_status`
BEFORE SELECT ON `cb_pausemenu_bans`
FOR EACH ROW
BEGIN
    UPDATE `cb_pausemenu_bans` 
    SET `active` = 0 
    WHERE `duration` > 0 AND `timestamp` + `duration` <= UNIX_TIMESTAMP() AND `active` = 1;
END //

DELIMITER ;
