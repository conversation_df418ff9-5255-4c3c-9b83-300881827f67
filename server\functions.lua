-- Server Functions for CB Pause Menu

-- Function to get player's Discord information
function GetPlayerDiscordInfo(source)
    local discordId = nil
    local avatar = nil
    
    for i = 0, GetNumPlayerIdentifiers(source) - 1 do
        local identifier = GetPlayerIdentifier(source, i)
        if string.find(identifier, "discord:") then
            discordId = string.gsub(identifier, "discord:", "")
            break
        end
    end
    
    if discordId and Config.DiscordBotToken ~= '' then
        -- Make API call to Discord to get user info
        PerformHttpRequest('https://discord.com/api/v9/users/' .. discordId, function(errorCode, resultData, resultHeaders)
            if errorCode == 200 then
                local userData = json.decode(resultData)
                if userData and userData.avatar then
                    avatar = 'https://cdn.discordapp.com/avatars/' .. discordId .. '/' .. userData.avatar .. '.png'
                    TriggerClientEvent('cb_pausemenu:updateDiscordAvatar', source, avatar)
                end
            end
        end, 'GET', '', {
            ['Authorization'] = 'Bot ' .. Config.DiscordBotToken,
            ['Content-Type'] = 'application/json'
        })
    end
    
    return {
        id = discordId,
        avatar = avatar or 'https://cdn.discordapp.com/embed/avatars/0.png'
    }
end

-- Function to log player actions
function LogPlayerAction(source, action, details)
    local user_id = vRP.getUserId({source})
    local playerName = GetPlayerName(source)
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')

    local logData = {
        identifier = user_id,
        playerName = playerName,
        action = action,
        details = details,
        timestamp = timestamp
    }

    -- Save to database
    MySQL.Async.execute('INSERT INTO cb_pausemenu_logs (identifier, player_name, action, details, timestamp) VALUES (@identifier, @player_name, @action, @details, @timestamp)', {
        ['@identifier'] = tostring(user_id),
        ['@player_name'] = playerName,
        ['@action'] = action,
        ['@details'] = json.encode(details),
        ['@timestamp'] = timestamp
    })

    -- Send to Discord webhook if configured
    if Config.DiscordWebhook ~= '' then
        SendDiscordLog(logData)
    end
end

-- Function to send Discord webhook
function SendDiscordLog(logData)
    local embed = {
        {
            title = "CB Pause Menu - Player Action",
            description = "Player: " .. logData.playerName .. "\nAction: " .. logData.action,
            color = 3447003,
            fields = {
                {
                    name = "Identifier",
                    value = logData.identifier,
                    inline = true
                },
                {
                    name = "Timestamp",
                    value = logData.timestamp,
                    inline = true
                }
            }
        }
    }
    
    if logData.details then
        table.insert(embed[1].fields, {
            name = "Details",
            value = json.encode(logData.details),
            inline = false
        })
    end
    
    PerformHttpRequest(Config.DiscordWebhook, function(err, text, headers) end, 'POST', json.encode({
        username = "CB Pause Menu",
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Function to get player statistics
function GetPlayerStatistics(identifier)
    local stats = {
        totalPlaytime = 0,
        sessionsCount = 0,
        lastSeen = 0,
        achievements = {},
        rank = 1
    }
    
    if playerData[identifier] then
        stats = playerData[identifier]
    end
    
    return stats
end

-- Function to update player achievement
function UpdatePlayerAchievement(source, achievementId)
    local identifier = GetPlayerIdentifier(source, 0)
    
    if not playerData[identifier] then
        playerData[identifier] = {
            playtime = 0,
            achievements = {},
            rank = 1
        }
    end
    
    if not playerData[identifier].achievements then
        playerData[identifier].achievements = {}
    end
    
    if not playerData[identifier].achievements[achievementId] then
        playerData[identifier].achievements[achievementId] = {
            unlocked = true,
            timestamp = os.time()
        }
        
        -- Save to database
        SavePlayerData(identifier, playerData[identifier])
        
        -- Notify player
        TriggerClientEvent('cb_pausemenu:notify', source, 'تم فتح إنجاز جديد!', 'success')
        TriggerClientEvent('cb_pausemenu:updateAchievements', source, playerData[identifier].achievements)
        
        -- Log achievement
        LogPlayerAction(source, 'achievement_unlocked', {achievementId = achievementId})
    end
end

-- Function to ban player
function BanPlayer(source, targetId, reason, duration)
    if not IsPlayerAdmin(source) then
        TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        return
    end
    
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    local targetName = GetPlayerName(targetId)
    
    if targetIdentifier then
        local banData = {
            identifier = targetIdentifier,
            playerName = targetName,
            reason = reason,
            duration = duration,
            bannedBy = GetPlayerName(source),
            timestamp = os.time()
        }
        
        -- Save ban to database
        if Config.Framework == 'esx' then
            MySQL.Async.execute('INSERT INTO cb_pausemenu_bans (identifier, player_name, reason, duration, banned_by, timestamp) VALUES (@identifier, @player_name, @reason, @duration, @banned_by, @timestamp)', {
                ['@identifier'] = targetIdentifier,
                ['@player_name'] = targetName,
                ['@reason'] = reason,
                ['@duration'] = duration,
                ['@banned_by'] = GetPlayerName(source),
                ['@timestamp'] = os.time()
            })
        end
        
        -- Kick player
        DropPlayer(targetId, 'تم حظرك من السيرفر. السبب: ' .. reason)
        
        -- Log ban
        LogPlayerAction(source, 'player_banned', banData)
        
        TriggerClientEvent('cb_pausemenu:notify', source, 'تم حظر اللاعب بنجاح', 'success')
    else
        TriggerClientEvent('cb_pausemenu:notify', source, 'لاعب غير موجود', 'error')
    end
end

-- Function to kick player
function KickPlayer(source, targetId, reason)
    if not IsPlayerAdmin(source) then
        TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        return
    end
    
    local targetName = GetPlayerName(targetId)
    
    if targetName then
        DropPlayer(targetId, 'تم طردك من السيرفر. السبب: ' .. reason)
        
        -- Log kick
        LogPlayerAction(source, 'player_kicked', {
            targetId = targetId,
            targetName = targetName,
            reason = reason
        })
        
        TriggerClientEvent('cb_pausemenu:notify', source, 'تم طرد اللاعب بنجاح', 'success')
    else
        TriggerClientEvent('cb_pausemenu:notify', source, 'لاعب غير موجود', 'error')
    end
end

-- Function to check if player is banned
function IsPlayerBanned(identifier)
    if Config.Framework == 'esx' then
        local result = MySQL.Sync.fetchAll('SELECT * FROM cb_pausemenu_bans WHERE identifier = @identifier AND (duration = 0 OR timestamp + duration > @current_time)', {
            ['@identifier'] = identifier,
            ['@current_time'] = os.time()
        })
        
        return #result > 0, result[1]
    end
    
    return false, nil
end

-- Function to get server statistics
function GetServerStatistics()
    local players = GetPlayers()
    local stats = {
        onlinePlayers = #players,
        maxPlayers = GetConvarInt('sv_maxclients', 32),
        uptime = GetGameTimer(),
        resourcesCount = GetNumResources(),
        serverName = Config.ServerName
    }
    
    return stats
end

-- Function to send server announcement
function SendServerAnnouncement(title, message, type)
    for _, playerId in ipairs(GetPlayers()) do
        TriggerClientEvent('cb_pausemenu:serverMessage', playerId, title, message, type)
    end
    
    -- Log announcement
    LogPlayerAction(-1, 'server_announcement', {
        title = title,
        message = message,
        type = type
    })
end

-- Function to toggle maintenance mode
function ToggleMaintenanceMode(enabled, message)
    Config.MaintenanceMode = enabled
    
    for _, playerId in ipairs(GetPlayers()) do
        TriggerClientEvent('cb_pausemenu:maintenanceMode', playerId, enabled, message)
    end
    
    -- Log maintenance mode change
    LogPlayerAction(-1, 'maintenance_mode', {
        enabled = enabled,
        message = message
    })
end

-- Function to get player's VIP status
function GetPlayerVIPStatus(source)
    local user_id = vRP.getUserId({source})

    if user_id then
        -- Check if player has VIP group
        local isVIP = vRP.hasGroup({user_id, "vip"}) or
                     vRP.hasGroup({user_id, "vip1"}) or
                     vRP.hasGroup({user_id, "vip2"}) or
                     vRP.hasGroup({user_id, "vip3"})

        local level = 0
        if vRP.hasGroup({user_id, "vip3"}) then level = 3
        elseif vRP.hasGroup({user_id, "vip2"}) then level = 2
        elseif vRP.hasGroup({user_id, "vip1"}) then level = 1
        elseif vRP.hasGroup({user_id, "vip"}) then level = 1
        end

        return {
            isVIP = isVIP,
            level = level,
            expiry = 0 -- vRP doesn't have expiry by default
        }
    end

    return {
        isVIP = false,
        level = 0,
        expiry = 0
    }
end

-- Function to give VIP to player
function GivePlayerVIP(source, targetId, level, duration)
    if not IsPlayerAdmin(source) then
        TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
        return
    end
    
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    
    if targetIdentifier then
        -- VIP logic would be implemented here
        TriggerClientEvent('cb_pausemenu:updateVIPStatus', targetId, true, level, os.time() + duration)
        TriggerClientEvent('cb_pausemenu:notify', source, 'تم منح VIP للاعب', 'success')
        
        -- Log VIP grant
        LogPlayerAction(source, 'vip_granted', {
            targetId = targetId,
            level = level,
            duration = duration
        })
    else
        TriggerClientEvent('cb_pausemenu:notify', source, 'لاعب غير موجود', 'error')
    end
end
