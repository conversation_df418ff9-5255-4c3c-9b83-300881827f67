-- Tunnel Library for CB Pause Menu
-- Advanced communication system between client and server

local Tunnel = {}

-- Tunnel configuration
Tunnel.config = {
    timeout = 60000, -- 60 seconds timeout
    debug = false
}

-- Storage for tunnel data
local tunnel_requests = {}
local tunnel_callbacks = {}
local tunnel_interfaces = {}

-- Generate unique request ID
local function generateRequestId()
    return math.random(100000, 999999) .. "_" .. GetGameTimer()
end

-- Debug logging
local function debugLog(message)
    if Tunnel.config.debug then
        print("^3[TUNNEL]^7 " .. message)
    end
end

-- Create tunnel interface
function Tunnel.getInterface(name, interface)
    if not tunnel_interfaces[name] then
        tunnel_interfaces[name] = {}
        
        -- Create proxy functions
        for key, value in pairs(interface or {}) do
            if type(value) == "function" then
                tunnel_interfaces[name][key] = function(...)
                    local args = {...}
                    local requestId = generateRequestId()
                    
                    debugLog("Sending tunnel request: " .. name .. "." .. key .. " (" .. requestId .. ")")
                    
                    -- Send request
                    TriggerEvent("tunnel:request", name, key, requestId, args)
                    
                    -- Wait for response (if not async)
                    local startTime = GetGameTimer()
                    while not tunnel_requests[requestId] and (GetGameTimer() - startTime) < Tunnel.config.timeout do
                        Citizen.Wait(0)
                    end
                    
                    if tunnel_requests[requestId] then
                        local result = tunnel_requests[requestId]
                        tunnel_requests[requestId] = nil
                        return table.unpack(result)
                    else
                        debugLog("Tunnel request timeout: " .. requestId)
                        return nil
                    end
                end
            end
        end
    end
    
    return tunnel_interfaces[name]
end

-- Register tunnel interface
function Tunnel.bindInterface(name, interface)
    tunnel_callbacks[name] = interface
    
    debugLog("Registered tunnel interface: " .. name)
end

-- Handle tunnel requests
RegisterNetEvent("tunnel:request")
AddEventHandler("tunnel:request", function(interfaceName, methodName, requestId, args)
    local source = source
    
    debugLog("Received tunnel request: " .. interfaceName .. "." .. methodName .. " (" .. requestId .. ")")
    
    if tunnel_callbacks[interfaceName] and tunnel_callbacks[interfaceName][methodName] then
        local success, result = pcall(tunnel_callbacks[interfaceName][methodName], table.unpack(args or {}))
        
        if success then
            TriggerEvent("tunnel:response", requestId, {result})
        else
            debugLog("Tunnel request error: " .. tostring(result))
            TriggerEvent("tunnel:response", requestId, {nil, result})
        end
    else
        debugLog("Tunnel interface not found: " .. interfaceName .. "." .. methodName)
        TriggerEvent("tunnel:response", requestId, {nil, "Interface not found"})
    end
end)

-- Handle tunnel responses
RegisterNetEvent("tunnel:response")
AddEventHandler("tunnel:response", function(requestId, result)
    tunnel_requests[requestId] = result
    debugLog("Received tunnel response: " .. requestId)
end)

-- Async tunnel call
function Tunnel.callAsync(interfaceName, methodName, args, callback)
    local requestId = generateRequestId()
    
    debugLog("Sending async tunnel request: " .. interfaceName .. "." .. methodName .. " (" .. requestId .. ")")
    
    -- Store callback
    tunnel_requests[requestId] = callback
    
    -- Send request
    TriggerEvent("tunnel:request", interfaceName, methodName, requestId, args)
end

-- Cleanup old requests
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Check every 30 seconds
        
        local currentTime = GetGameTimer()
        for requestId, _ in pairs(tunnel_requests) do
            local requestTime = tonumber(string.split(requestId, "_")[2])
            if requestTime and (currentTime - requestTime) > Tunnel.config.timeout then
                tunnel_requests[requestId] = nil
                debugLog("Cleaned up old tunnel request: " .. requestId)
            end
        end
    end
end)

-- Export tunnel
_G.Tunnel = Tunnel

return Tunnel
