local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP","cb_pausemenu")

local isMenuOpen = false
local playerData = {}
local currentFPS = 0
local playtime = 0
local playerRank = {}

-- vRP Player loaded event
AddEventHandler("vRP:playerSpawn", function(user_id, source, first_spawn)
    if first_spawn then
        playerData.user_id = user_id
        TriggerServerEvent('cb_pausemenu:getPlayerData')
    end
end)

-- Alternative for older vRP versions
RegisterNetEvent("vRP:playerLoaded")
AddEventHandler("vRP:playerLoaded", function()
    Citizen.Wait(1000) -- Wait for vRP to fully load
    TriggerServerEvent('cb_pausemenu:getPlayerData')
end)

-- Main thread for FPS monitoring
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(Config.FPSUpdateInterval)
        currentFPS = math.floor(1.0 / GetFrameTime())
        
        if isMenuOpen then
            SendNUIMessage({
                type = 'updateFPS',
                fps = currentFPS
            })
        end
    end
end)

-- Key mapping for menu
RegisterKeyMapping('pausemenu', 'Open Pause Menu', 'keyboard', Config.MenuKey)

-- Command to open menu
RegisterCommand('pausemenu', function()
    ToggleMenu()
end, false)

-- Function to toggle menu
function ToggleMenu()
    if isMenuOpen then
        CloseMenu()
    else
        OpenMenu()
    end
end

-- Function to open menu
function OpenMenu()
    if isMenuOpen then return end
    
    isMenuOpen = true
    SetNuiFocus(true, true)
    
    -- Get player data
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)
    
    -- Get player money (vRP)
    local money = 0
    local bank = 0

    if Config.Framework == 'vrp' then
        vRP.getUserId({}, function(user_id)
            if user_id then
                vRP.getMoney({user_id}, function(wallet)
                    money = wallet or 0
                end)
                vRP.getBankMoney({user_id}, function(bankMoney)
                    bank = bankMoney or 0
                end)
            end
        end)
    end
    
    -- Calculate rank
    playerRank = CalculatePlayerRank(playtime)
    
    -- Send data to NUI
    SendNUIMessage({
        type = 'openMenu',
        data = {
            playerName = GetPlayerName(PlayerId()),
            playerId = GetPlayerServerId(PlayerId()),
            playtime = FormatPlaytime(playtime),
            rank = playerRank,
            money = money,
            bank = bank,
            job = playerData.job or {name = 'unemployed', label = 'عاطل'},
            fps = currentFPS,
            coords = {x = playerCoords.x, y = playerCoords.y, z = playerCoords.z},
            heading = playerHeading,
            serverName = Config.ServerName,
            maxPlayers = Config.MaxPlayers,
            currentPlayers = #GetActivePlayers(),
            discordAvatar = GetDiscordAvatar(),
            config = Config
        }
    })
    
    -- Disable controls
    DisableControlsWhileMenuOpen()
end

-- Function to close menu
function CloseMenu()
    if not isMenuOpen then return end
    
    isMenuOpen = false
    SetNuiFocus(false, false)
    
    SendNUIMessage({
        type = 'closeMenu'
    })
end

-- Function to calculate player rank
function CalculatePlayerRank(playtimeHours)
    local rank = Config.Ranks[1] -- Default to first rank
    
    for i = #Config.Ranks, 1, -1 do
        if playtimeHours >= Config.Ranks[i].minPlaytime then
            rank = Config.Ranks[i]
            break
        end
    end
    
    return rank
end

-- Function to format playtime
function FormatPlaytime(hours)
    if hours < 1 then
        return math.floor(hours * 60) .. " دقيقة"
    elseif hours < 24 then
        return math.floor(hours) .. " ساعة"
    else
        local days = math.floor(hours / 24)
        local remainingHours = math.floor(hours % 24)
        return days .. " يوم " .. remainingHours .. " ساعة"
    end
end

-- Function to get Discord avatar
function GetDiscordAvatar()
    -- This would need to be implemented with Discord API
    return "https://cdn.discordapp.com/embed/avatars/0.png"
end

-- Function to disable controls while menu is open
function DisableControlsWhileMenuOpen()
    Citizen.CreateThread(function()
        while isMenuOpen do
            Citizen.Wait(0)
            
            -- Disable all controls
            DisableAllControlActions(0)
            
            -- Enable cursor controls
            EnableControlAction(0, 1, true) -- LookLeftRight
            EnableControlAction(0, 2, true) -- LookUpDown
            EnableControlAction(0, 142, true) -- MeleeAttackAlternate
            EnableControlAction(0, 18, true) -- Enter
            EnableControlAction(0, 322, true) -- ESC
            EnableControlAction(0, 106, true) -- VehicleMouseControlOverride
        end
    end)
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    CloseMenu()
    cb('ok')
end)

RegisterNUICallback('disconnect', function(data, cb)
    TriggerServerEvent('cb_pausemenu:disconnect')
    cb('ok')
end)

RegisterNUICallback('teleportToWaypoint', function(data, cb)
    TeleportToWaypoint()
    cb('ok')
end)

-- Function to teleport to waypoint
function TeleportToWaypoint()
    local waypoint = GetFirstBlipInfoId(8)
    if DoesBlipExist(waypoint) then
        local coords = GetBlipInfoIdCoord(waypoint)
        local ground, z = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z, false)
        
        if ground then
            coords.z = z
        end
        
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)
        vRP.notify({GetPlayerServerId(PlayerId()), 'تم النقل إلى النقطة المحددة'})
    else
        vRP.notify({GetPlayerServerId(PlayerId()), 'لم يتم تحديد نقطة على الخريطة'})
    end
end

-- Events
RegisterNetEvent('cb_pausemenu:updatePlayerData')
AddEventHandler('cb_pausemenu:updatePlayerData', function(data)
    playtime = data.playtime or 0
    playerData = data.playerData or {}
end)
