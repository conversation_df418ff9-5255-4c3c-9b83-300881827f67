local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP","cb_pausemenu")

-- Player data storage
local playerData = {}
local playerPlaytime = {}

-- Server startup
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('^2[CB_PAUSEMENU]^7 Resource started successfully!')
        
        -- Load player data from database
        LoadAllPlayerData()
        
        -- Start playtime tracking
        StartPlaytimeTracking()
    end
end)

-- vRP Player connecting
AddEventHandler("vRP:playerJoin", function(user_id, source, name, last_login)
    local identifier = vRP.getUserId({source})

    if identifier then
        -- Initialize player data
        if not playerData[identifier] then
            playerData[identifier] = {
                playtime = 0,
                lastSeen = os.time(),
                rank = 1,
                achievements = {},
                settings = {}
            }
        end

        playerPlaytime[source] = {
            identifier = identifier,
            user_id = user_id,
            joinTime = os.time()
        }
    end
end)

-- vRP Player disconnecting
AddEventHandler("vRP:playerLeave", function(user_id, source)
    local identifier = user_id

    if playerPlaytime[source] then
        local sessionTime = os.time() - playerPlaytime[source].joinTime

        if playerData[identifier] then
            playerData[identifier].playtime = playerData[identifier].playtime + (sessionTime / 3600) -- Convert to hours
            playerData[identifier].lastSeen = os.time()

            -- Save to database
            SavePlayerData(identifier, playerData[identifier])
        end

        playerPlaytime[source] = nil
    end
end)

-- Function to load all player data
function LoadAllPlayerData()
    MySQL.Async.fetchAll('SELECT * FROM cb_pausemenu_data', {}, function(results)
        for i = 1, #results do
            local data = results[i]
            playerData[data.identifier] = json.decode(data.data)
        end
    end)
end

-- Function to save player data
function SavePlayerData(identifier, data)
    MySQL.Async.execute('INSERT INTO cb_pausemenu_data (identifier, data) VALUES (@identifier, @data) ON DUPLICATE KEY UPDATE data = @data', {
        ['@identifier'] = tostring(identifier),
        ['@data'] = json.encode(data)
    })
end

-- Function to start playtime tracking
function StartPlaytimeTracking()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(60000) -- Update every minute
            
            for source, data in pairs(playerPlaytime) do
                if GetPlayerName(source) then
                    local identifier = data.identifier
                    local sessionTime = os.time() - data.joinTime
                    
                    if playerData[identifier] then
                        local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
                        
                        -- Send updated data to client
                        TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
                            playtime = totalPlaytime,
                            playerData = playerData[identifier]
                        })
                    end
                end
            end
        end
    end)
end

-- Event: Get player data
RegisterServerEvent('cb_pausemenu:getPlayerData')
AddEventHandler('cb_pausemenu:getPlayerData', function()
    local source = source
    local user_id = vRP.getUserId({source})

    if user_id and playerData[user_id] then
        local sessionTime = 0
        if playerPlaytime[source] then
            sessionTime = os.time() - playerPlaytime[source].joinTime
        end

        local totalPlaytime = playerData[user_id].playtime + (sessionTime / 3600)

        -- Get vRP player data
        local money = vRP.getMoney({user_id}) or 0
        local bank = vRP.getBankMoney({user_id}) or 0
        local group = vRP.getUserGroupByType({user_id, "job"}) or "unemployed"

        TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
            playtime = totalPlaytime,
            playerData = playerData[user_id],
            money = money,
            bank = bank,
            job = {name = group, label = group}
        })
    end
end)

-- Event: Player disconnect
RegisterServerEvent('cb_pausemenu:disconnect')
AddEventHandler('cb_pausemenu:disconnect', function()
    local source = source
    DropPlayer(source, 'تم قطع الاتصال بواسطة اللاعب')
end)

-- Event: Send friend request
RegisterServerEvent('cb_pausemenu:sendFriendRequest')
AddEventHandler('cb_pausemenu:sendFriendRequest', function(targetId)
    local source = source
    local sourceIdentifier = GetPlayerIdentifier(source, 0)
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    
    if sourceIdentifier and targetIdentifier then
        -- Add friend request logic here
        TriggerClientEvent('cb_pausemenu:friendRequestResponse', source, true, 'تم إرسال طلب الصداقة')
    else
        TriggerClientEvent('cb_pausemenu:friendRequestResponse', source, false, 'لاعب غير موجود')
    end
end)

-- Event: Toggle god mode (admin only)
RegisterServerEvent('cb_pausemenu:toggleGodMode')
AddEventHandler('cb_pausemenu:toggleGodMode', function()
    local source = source
    
    if IsPlayerAdmin(source) then
        -- Toggle god mode logic here
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'godmode', true, 'تم تفعيل/إلغاء وضع الحماية')
    else
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'godmode', false, 'ليس لديك صلاحية')
    end
end)

-- Event: Toggle invisible mode (admin only)
RegisterServerEvent('cb_pausemenu:toggleInvisible')
AddEventHandler('cb_pausemenu:toggleInvisible', function()
    local source = source
    
    if IsPlayerAdmin(source) then
        -- Toggle invisible mode logic here
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'invisible', true, 'تم تفعيل/إلغاء وضع الاختفاء')
    else
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'invisible', false, 'ليس لديك صلاحية')
    end
end)

-- Event: Teleport to player (admin only)
RegisterServerEvent('cb_pausemenu:teleportToPlayer')
AddEventHandler('cb_pausemenu:teleportToPlayer', function(targetId)
    local source = source
    
    if IsPlayerAdmin(source) then
        if GetPlayerName(targetId) then
            -- Teleport logic would be handled on client side
            TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'teleport', true, 'تم النقل إلى اللاعب')
        else
            TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'teleport', false, 'لاعب غير موجود')
        end
    else
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'teleport', false, 'ليس لديك صلاحية')
    end
end)

-- Function to check if player is admin
function IsPlayerAdmin(source)
    local user_id = vRP.getUserId({source})

    if user_id then
        return vRP.hasGroup({user_id, "admin"}) or
               vRP.hasGroup({user_id, "superadmin"}) or
               vRP.hasGroup({user_id, "moderator"}) or
               vRP.hasPermission({user_id, "admin.menu"})
    end

    return false
end

-- Function to get online players
function GetOnlinePlayers()
    local players = {}
    
    for _, playerId in ipairs(GetPlayers()) do
        local name = GetPlayerName(playerId)
        if name then
            table.insert(players, {
                id = playerId,
                name = name,
                ping = GetPlayerPing(playerId)
            })
        end
    end
    
    return players
end

-- Command to get server info
RegisterCommand('serverinfo', function(source, args, rawCommand)
    local players = GetOnlinePlayers()
    local maxPlayers = GetConvarInt('sv_maxclients', 32)
    
    TriggerClientEvent('cb_pausemenu:updateServerPerformance', source, {
        players = #players,
        maxPlayers = maxPlayers,
        uptime = GetGameTimer()
    })
end, false)

-- Periodic server performance update
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Every 30 seconds
        
        local players = GetOnlinePlayers()
        local performance = {
            players = #players,
            maxPlayers = GetConvarInt('sv_maxclients', 32),
            uptime = GetGameTimer()
        }
        
        for _, playerId in ipairs(GetPlayers()) do
            TriggerClientEvent('cb_pausemenu:updateServerPerformance', playerId, performance)
        end
    end
end)
