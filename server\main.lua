local ESX = nil
local QBCore = nil

-- Initialize Framework
if Config.Framework == 'esx' then
    ESX = exports["es_extended"]:getSharedObject()
elseif Config.Framework == 'qb' then
    QBCore = exports['qb-core']:GetCoreObject()
end

-- Player data storage
local playerData = {}
local playerPlaytime = {}

-- Server startup
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('^2[CB_PAUSEMENU]^7 Resource started successfully!')
        
        -- Load player data from database
        LoadAllPlayerData()
        
        -- Start playtime tracking
        StartPlaytimeTracking()
    end
end)

-- Player connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    -- Initialize player data
    if not playerData[identifier] then
        playerData[identifier] = {
            playtime = 0,
            lastSeen = os.time(),
            rank = 1,
            achievements = {},
            settings = {}
        }
    end
    
    playerPlaytime[source] = {
        identifier = identifier,
        joinTime = os.time()
    }
end)

-- Player disconnecting
AddEventHandler('playerDropped', function(reason)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if playerPlaytime[source] then
        local sessionTime = os.time() - playerPlaytime[source].joinTime
        
        if playerData[identifier] then
            playerData[identifier].playtime = playerData[identifier].playtime + (sessionTime / 3600) -- Convert to hours
            playerData[identifier].lastSeen = os.time()
            
            -- Save to database
            SavePlayerData(identifier, playerData[identifier])
        end
        
        playerPlaytime[source] = nil
    end
end)

-- Function to load all player data
function LoadAllPlayerData()
    if Config.Framework == 'esx' then
        MySQL.Async.fetchAll('SELECT * FROM cb_pausemenu_data', {}, function(results)
            for i = 1, #results do
                local data = results[i]
                playerData[data.identifier] = json.decode(data.data)
            end
        end)
    end
end

-- Function to save player data
function SavePlayerData(identifier, data)
    if Config.Framework == 'esx' then
        MySQL.Async.execute('INSERT INTO cb_pausemenu_data (identifier, data) VALUES (@identifier, @data) ON DUPLICATE KEY UPDATE data = @data', {
            ['@identifier'] = identifier,
            ['@data'] = json.encode(data)
        })
    end
end

-- Function to start playtime tracking
function StartPlaytimeTracking()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(60000) -- Update every minute
            
            for source, data in pairs(playerPlaytime) do
                if GetPlayerName(source) then
                    local identifier = data.identifier
                    local sessionTime = os.time() - data.joinTime
                    
                    if playerData[identifier] then
                        local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
                        
                        -- Send updated data to client
                        TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
                            playtime = totalPlaytime,
                            playerData = playerData[identifier]
                        })
                    end
                end
            end
        end
    end)
end

-- Event: Get player data
RegisterServerEvent('cb_pausemenu:getPlayerData')
AddEventHandler('cb_pausemenu:getPlayerData', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if playerData[identifier] then
        local sessionTime = 0
        if playerPlaytime[source] then
            sessionTime = os.time() - playerPlaytime[source].joinTime
        end
        
        local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
        
        TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
            playtime = totalPlaytime,
            playerData = playerData[identifier]
        })
    end
end)

-- Event: Player disconnect
RegisterServerEvent('cb_pausemenu:disconnect')
AddEventHandler('cb_pausemenu:disconnect', function()
    local source = source
    DropPlayer(source, 'تم قطع الاتصال بواسطة اللاعب')
end)

-- Event: Send friend request
RegisterServerEvent('cb_pausemenu:sendFriendRequest')
AddEventHandler('cb_pausemenu:sendFriendRequest', function(targetId)
    local source = source
    local sourceIdentifier = GetPlayerIdentifier(source, 0)
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    
    if sourceIdentifier and targetIdentifier then
        -- Add friend request logic here
        TriggerClientEvent('cb_pausemenu:friendRequestResponse', source, true, 'تم إرسال طلب الصداقة')
    else
        TriggerClientEvent('cb_pausemenu:friendRequestResponse', source, false, 'لاعب غير موجود')
    end
end)

-- Event: Toggle god mode (admin only)
RegisterServerEvent('cb_pausemenu:toggleGodMode')
AddEventHandler('cb_pausemenu:toggleGodMode', function()
    local source = source
    
    if IsPlayerAdmin(source) then
        -- Toggle god mode logic here
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'godmode', true, 'تم تفعيل/إلغاء وضع الحماية')
    else
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'godmode', false, 'ليس لديك صلاحية')
    end
end)

-- Event: Toggle invisible mode (admin only)
RegisterServerEvent('cb_pausemenu:toggleInvisible')
AddEventHandler('cb_pausemenu:toggleInvisible', function()
    local source = source
    
    if IsPlayerAdmin(source) then
        -- Toggle invisible mode logic here
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'invisible', true, 'تم تفعيل/إلغاء وضع الاختفاء')
    else
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'invisible', false, 'ليس لديك صلاحية')
    end
end)

-- Event: Teleport to player (admin only)
RegisterServerEvent('cb_pausemenu:teleportToPlayer')
AddEventHandler('cb_pausemenu:teleportToPlayer', function(targetId)
    local source = source
    
    if IsPlayerAdmin(source) then
        if GetPlayerName(targetId) then
            -- Teleport logic would be handled on client side
            TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'teleport', true, 'تم النقل إلى اللاعب')
        else
            TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'teleport', false, 'لاعب غير موجود')
        end
    else
        TriggerClientEvent('cb_pausemenu:adminActionResponse', source, 'teleport', false, 'ليس لديك صلاحية')
    end
end)

-- Function to check if player is admin
function IsPlayerAdmin(source)
    local identifier = GetPlayerIdentifier(source, 0)
    
    if Config.Framework == 'esx' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            return xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'superadmin'
        end
    elseif Config.Framework == 'qb' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player then
            return QBCore.Functions.HasPermission(source, 'admin')
        end
    end
    
    return false
end

-- Function to get online players
function GetOnlinePlayers()
    local players = {}
    
    for _, playerId in ipairs(GetPlayers()) do
        local name = GetPlayerName(playerId)
        if name then
            table.insert(players, {
                id = playerId,
                name = name,
                ping = GetPlayerPing(playerId)
            })
        end
    end
    
    return players
end

-- Command to get server info
RegisterCommand('serverinfo', function(source, args, rawCommand)
    local players = GetOnlinePlayers()
    local maxPlayers = GetConvarInt('sv_maxclients', 32)
    
    TriggerClientEvent('cb_pausemenu:updateServerPerformance', source, {
        players = #players,
        maxPlayers = maxPlayers,
        uptime = GetGameTimer()
    })
end, false)

-- Periodic server performance update
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Every 30 seconds
        
        local players = GetOnlinePlayers()
        local performance = {
            players = #players,
            maxPlayers = GetConvarInt('sv_maxclients', 32),
            uptime = GetGameTimer()
        }
        
        for _, playerId in ipairs(GetPlayers()) do
            TriggerClientEvent('cb_pausemenu:updateServerPerformance', playerId, performance)
        end
    end
end)
