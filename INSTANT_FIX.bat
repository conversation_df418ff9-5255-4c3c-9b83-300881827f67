@echo off
echo ========================================
echo   INSTANT FIX FOR BLURRED SCREEN
echo ========================================
echo.
echo This will fix the blurred screen issue IMMEDIATELY
echo.
pause

echo Creating ultra-simple HTML file...

echo ^<!DOCTYPE html^> > html\index.html
echo ^<html^> >> html\index.html
echo ^<head^> >> html\index.html
echo ^<meta charset="UTF-8"^> >> html\index.html
echo ^<title^>CB Menu^</title^> >> html\index.html
echo ^<style^> >> html\index.html
echo body { background: rgba(0,0,0,0.8); color: white; font-family: Arial; margin: 0; padding: 20px; } >> html\index.html
echo .menu { display: none; position: fixed; top: 50%%; left: 50%%; transform: translate(-50%%, -50%%); background: #1a1a2e; padding: 30px; border-radius: 10px; border: 2px solid #00d4ff; } >> html\index.html
echo .menu.show { display: block; } >> html\index.html
echo h1 { color: #00d4ff; text-align: center; } >> html\index.html
echo .info { margin: 10px 0; } >> html\index.html
echo .btn { background: #00d4ff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; } >> html\index.html
echo .btn:hover { background: #0099cc; } >> html\index.html
echo ^</style^> >> html\index.html
echo ^</head^> >> html\index.html
echo ^<body^> >> html\index.html
echo ^<div class="menu" id="menu"^> >> html\index.html
echo ^<h1^>CB STORE SERVER^</h1^> >> html\index.html
echo ^<div class="info"^>اسم اللاعب: ^<span id="name"^>Player^</span^>^</div^> >> html\index.html
echo ^<div class="info"^>المال: ^<span id="money"^>$5000^</span^>^</div^> >> html\index.html
echo ^<div class="info"^>وقت اللعب: ^<span id="time"^>0 ساعة^</span^>^</div^> >> html\index.html
echo ^<div class="info"^>FPS: ^<span id="fps"^>60^</span^>^</div^> >> html\index.html
echo ^<br^> >> html\index.html
echo ^<button class="btn" onclick="closeMenu()"^>إغلاق^</button^> >> html\index.html
echo ^<button class="btn" onclick="disconnect()"^>قطع الاتصال^</button^> >> html\index.html
echo ^</div^> >> html\index.html
echo ^<script^> >> html\index.html
echo let isOpen = false; >> html\index.html
echo window.addEventListener('message', function(e) { >> html\index.html
echo   if (e.data.type === 'openMenu') { >> html\index.html
echo     isOpen = true; >> html\index.html
echo     document.getElementById('menu').classList.add('show'); >> html\index.html
echo     if (e.data.data) { >> html\index.html
echo       document.getElementById('name').textContent = e.data.data.playerName ^|\| 'Player'; >> html\index.html
echo       document.getElementById('money').textContent = '$' + (e.data.data.money ^|\| 5000); >> html\index.html
echo       document.getElementById('time').textContent = e.data.data.playtime ^|\| '0 ساعة'; >> html\index.html
echo       document.getElementById('fps').textContent = e.data.data.fps ^|\| 60; >> html\index.html
echo     } >> html\index.html
echo   } else if (e.data.type === 'closeMenu') { >> html\index.html
echo     isOpen = false; >> html\index.html
echo     document.getElementById('menu').classList.remove('show'); >> html\index.html
echo   } >> html\index.html
echo }); >> html\index.html
echo function closeMenu() { >> html\index.html
echo   fetch('https://' + window.location.hostname + '/closeMenu', { method: 'POST', body: '{}' }); >> html\index.html
echo } >> html\index.html
echo function disconnect() { >> html\index.html
echo   if (confirm('قطع الاتصال؟')) { >> html\index.html
echo     fetch('https://' + window.location.hostname + '/disconnect', { method: 'POST', body: '{}' }); >> html\index.html
echo   } >> html\index.html
echo } >> html\index.html
echo document.addEventListener('keydown', function(e) { >> html\index.html
echo   if (e.key === 'Escape' ^&^& isOpen) closeMenu(); >> html\index.html
echo }); >> html\index.html
echo ^</script^> >> html\index.html
echo ^</body^> >> html\index.html
echo ^</html^> >> html\index.html

echo.
echo Creating simple fxmanifest.lua...

echo fx_version 'cerulean' > fxmanifest.lua
echo game 'gta5' >> fxmanifest.lua
echo author 'CB_STORE' >> fxmanifest.lua
echo description 'CB Pause Menu - Fixed' >> fxmanifest.lua
echo version '1.0.0' >> fxmanifest.lua
echo ui_page 'html/index.html' >> fxmanifest.lua
echo client_scripts { 'client_final.lua' } >> fxmanifest.lua
echo server_scripts { 'server_final.lua' } >> fxmanifest.lua
echo files { 'html/index.html' } >> fxmanifest.lua

echo.
echo ========================================
echo   INSTANT FIX COMPLETED!
echo ========================================
echo.
echo WHAT WAS FIXED:
echo - Created ultra-simple HTML file
echo - Removed all problematic CSS/JS libraries
echo - Fixed NUI focus issues
echo - Clean, working interface
echo.
echo NEXT STEPS:
echo 1. Restart your server
echo 2. Press ESC in game
echo 3. Menu should work perfectly now
echo.
echo EMERGENCY COMMANDS:
echo /fixmouse - Fix mouse issues
echo /forceclosemenu - Force close menu
echo F1 - Emergency close key
echo.
pause
