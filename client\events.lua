-- Client Events for CB Pause Menu

-- Event when player data is updated
RegisterNetEvent('cb_pausemenu:updatePlayerData')
AddEventHandler('cb_pausemenu:updatePlayerData', function(data)
    playtime = data.playtime or 0
    playerData = data.playerData or {}
    
    if isMenuOpen then
        SendNUIMessage({
            type = 'updatePlayerData',
            data = {
                playtime = FormatPlaytime(playtime),
                rank = CalculatePlayerRank(playtime),
                playerData = playerData
            }
        })
    end
end)

-- Event when player money is updated
RegisterNetEvent('cb_pausemenu:updateMoney')
AddEventHandler('cb_pausemenu:updateMoney', function(money, bank)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateMoney',
            money = money,
            bank = bank
        })
    end
end)

-- Event when player job is updated
RegisterNetEvent('cb_pausemenu:updateJob')
AddEventHandler('cb_pausemenu:updateJob', function(job)
    playerData.job = job
    
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateJob',
            job = job
        })
    end
end)

-- Event for server notifications
RegisterNetEvent('cb_pausemenu:notify')
AddEventHandler('cb_pausemenu:notify', function(message, type, duration)
    CreateNotification(message, type or 'info', duration or 5000)
end)

-- Event for admin actions response
RegisterNetEvent('cb_pausemenu:adminActionResponse')
AddEventHandler('cb_pausemenu:adminActionResponse', function(action, success, message)
    if success then
        CreateNotification(message or 'تم تنفيذ الإجراء بنجاح', 'success')
    else
        CreateNotification(message or 'فشل في تنفيذ الإجراء', 'error')
    end
end)

-- Event for friend request response
RegisterNetEvent('cb_pausemenu:friendRequestResponse')
AddEventHandler('cb_pausemenu:friendRequestResponse', function(success, message)
    if success then
        CreateNotification('تم إرسال طلب الصداقة', 'success')
    else
        CreateNotification(message or 'فشل في إرسال طلب الصداقة', 'error')
    end
end)

-- Event for online friends update
RegisterNetEvent('cb_pausemenu:updateOnlineFriends')
AddEventHandler('cb_pausemenu:updateOnlineFriends', function(friends)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateOnlineFriends',
            friends = friends
        })
    end
end)

-- Event for server performance update
RegisterNetEvent('cb_pausemenu:updateServerPerformance')
AddEventHandler('cb_pausemenu:updateServerPerformance', function(performance)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateServerPerformance',
            performance = performance
        })
    end
end)

-- Event for Discord avatar update
RegisterNetEvent('cb_pausemenu:updateDiscordAvatar')
AddEventHandler('cb_pausemenu:updateDiscordAvatar', function(avatarUrl)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateDiscordAvatar',
            avatar = avatarUrl
        })
    end
end)

-- Event for player achievements update
RegisterNetEvent('cb_pausemenu:updateAchievements')
AddEventHandler('cb_pausemenu:updateAchievements', function(achievements)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateAchievements',
            achievements = achievements
        })
    end
end)

-- Event for weather change
RegisterNetEvent('cb_pausemenu:weatherChanged')
AddEventHandler('cb_pausemenu:weatherChanged', function(weather)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateWeather',
            weather = weather
        })
    end
end)

-- Event for time change
RegisterNetEvent('cb_pausemenu:timeChanged')
AddEventHandler('cb_pausemenu:timeChanged', function(time)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateTime',
            time = time
        })
    end
end)

-- Event for player stats update
RegisterNetEvent('cb_pausemenu:updatePlayerStats')
AddEventHandler('cb_pausemenu:updatePlayerStats', function()
    if isMenuOpen then
        local stats = GetPlayerStats()
        local vehicleInfo = GetVehicleInfo()
        local locationInfo = GetLocationInfo()
        local environmentInfo = GetEnvironmentInfo()
        
        SendNUIMessage({
            type = 'updatePlayerStats',
            stats = stats,
            vehicle = vehicleInfo,
            location = locationInfo,
            environment = environmentInfo
        })
    end
end)

-- Event for inventory update
RegisterNetEvent('cb_pausemenu:updateInventory')
AddEventHandler('cb_pausemenu:updateInventory', function()
    if isMenuOpen then
        local inventory = GetPlayerInventory()
        local weapons = GetPlayerWeapons()
        
        SendNUIMessage({
            type = 'updateInventory',
            inventory = inventory,
            weapons = weapons
        })
    end
end)

-- Event for screenshot taken
RegisterNetEvent('cb_pausemenu:screenshotTaken')
AddEventHandler('cb_pausemenu:screenshotTaken', function(url)
    CreateNotification('تم حفظ لقطة الشاشة: ' .. url, 'success')
end)

-- Event for player list update
RegisterNetEvent('cb_pausemenu:updatePlayerList')
AddEventHandler('cb_pausemenu:updatePlayerList', function(players)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updatePlayerList',
            players = players
        })
    end
end)

-- Event for server message
RegisterNetEvent('cb_pausemenu:serverMessage')
AddEventHandler('cb_pausemenu:serverMessage', function(title, message, type)
    if isMenuOpen then
        SendNUIMessage({
            type = 'serverMessage',
            title = title,
            message = message,
            messageType = type or 'info'
        })
    end
end)

-- Event for maintenance mode
RegisterNetEvent('cb_pausemenu:maintenanceMode')
AddEventHandler('cb_pausemenu:maintenanceMode', function(enabled, message)
    if enabled then
        CreateNotification('السيرفر في وضع الصيانة: ' .. (message or ''), 'warning')
    else
        CreateNotification('تم انتهاء وضع الصيانة', 'success')
    end
end)

-- Event for player ban/kick
RegisterNetEvent('cb_pausemenu:playerPunished')
AddEventHandler('cb_pausemenu:playerPunished', function(type, reason, duration)
    if type == 'kick' then
        CreateNotification('تم طردك من السيرفر: ' .. reason, 'error')
    elseif type == 'ban' then
        CreateNotification('تم حظرك من السيرفر: ' .. reason .. ' لمدة: ' .. duration, 'error')
    end
end)

-- Event for VIP status update
RegisterNetEvent('cb_pausemenu:updateVIPStatus')
AddEventHandler('cb_pausemenu:updateVIPStatus', function(isVIP, vipLevel, expiry)
    if isMenuOpen then
        SendNUIMessage({
            type = 'updateVIPStatus',
            isVIP = isVIP,
            vipLevel = vipLevel,
            expiry = expiry
        })
    end
end)
