-- Proxy Library for CB Pause Menu
-- Simplified interface for resource communication

local Proxy = {}

-- Proxy configuration
Proxy.config = {
    timeout = 30000, -- 30 seconds timeout
    debug = false
}

-- Storage for proxy data
local proxy_rdata = {}
local proxy_callbacks = {}
local proxy_rdata_timeout = {}

-- Debug logging
local function debugLog(message)
    if Proxy.config.debug then
        print("^2[PROXY]^7 " .. message)
    end
end

-- Generate unique request ID
local function generateRequestId()
    return "proxy_" .. math.random(100000, 999999) .. "_" .. GetGameTimer()
end

-- Get interface
function Proxy.getInterface(name, interface)
    local r = setmetatable({}, {
        __index = function(t, k)
            local rk = name .. ":" .. k
            if not proxy_callbacks[rk] then
                proxy_callbacks[rk] = function(...)
                    local args = {...}
                    local requestId = generateRequestId()
                    
                    debugLog("Sending proxy request: " .. rk .. " (" .. requestId .. ")")
                    
                    TriggerEvent(rk, requestId, source, ...)
                    
                    -- Wait for response
                    local startTime = GetGameTimer()
                    while proxy_rdata[requestId] == nil and (GetGameTimer() - startTime) < Proxy.config.timeout do
                        Citizen.Wait(0)
                    end
                    
                    if proxy_rdata[requestId] ~= nil then
                        local result = proxy_rdata[requestId]
                        proxy_rdata[requestId] = nil
                        proxy_rdata_timeout[requestId] = nil
                        return table.unpack(result)
                    else
                        debugLog("Proxy request timeout: " .. requestId)
                        return nil
                    end
                end
            end
            return proxy_callbacks[rk]
        end
    })
    
    return r
end

-- Add interface
function Proxy.addInterface(name, interface)
    for k, v in pairs(interface) do
        local rk = name .. ":" .. k
        RegisterNetEvent(rk)
        AddEventHandler(rk, function(requestId, source, ...)
            debugLog("Received proxy request: " .. rk .. " (" .. requestId .. ")")
            
            local success, result = pcall(v, ...)
            
            if success then
                TriggerEvent("proxy:response", requestId, {result})
            else
                debugLog("Proxy request error: " .. tostring(result))
                TriggerEvent("proxy:response", requestId, {nil, result})
            end
        end)
    end
    
    debugLog("Registered proxy interface: " .. name)
end

-- Handle proxy responses
RegisterNetEvent("proxy:response")
AddEventHandler("proxy:response", function(requestId, result)
    proxy_rdata[requestId] = result
    proxy_rdata_timeout[requestId] = GetGameTimer()
    debugLog("Received proxy response: " .. requestId)
end)

-- Async proxy call
function Proxy.callAsync(name, method, args, callback)
    local rk = name .. ":" .. method
    local requestId = generateRequestId()
    
    debugLog("Sending async proxy request: " .. rk .. " (" .. requestId .. ")")
    
    -- Store callback
    proxy_rdata[requestId] = callback
    
    -- Send request
    TriggerEvent(rk, requestId, source, table.unpack(args or {}))
end

-- Cleanup old requests
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Check every 30 seconds
        
        local currentTime = GetGameTimer()
        for requestId, timestamp in pairs(proxy_rdata_timeout) do
            if (currentTime - timestamp) > Proxy.config.timeout then
                proxy_rdata[requestId] = nil
                proxy_rdata_timeout[requestId] = nil
                debugLog("Cleaned up old proxy request: " .. requestId)
            end
        end
    end
end)

-- Export proxy
_G.Proxy = Proxy

return Proxy
