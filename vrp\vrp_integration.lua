-- vRP Integration for CB Pause Menu
-- This file handles all vRP specific functions and integrations

local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP","cb_pausemenu")

-- vRP Integration Class
local vRPIntegration = {}

-- Get player identifier (user_id in vRP)
function vRPIntegration.getPlayerIdentifier(source)
    return vRP.getUserId({source})
end

-- Get player name
function vRPIntegration.getPlayerName(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        return vRP.getPlayerName({user_id}) or GetPlayerName(source)
    end
    return GetPlayerName(source)
end

-- Get player money
function vRPIntegration.getPlayerMoney(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local wallet = vRP.getMoney({user_id}) or 0
        local bank = vRP.getBankMoney({user_id}) or 0
        return {
            cash = wallet,
            bank = bank,
            total = wallet + bank
        }
    end
    return {cash = 0, bank = 0, total = 0}
end

-- Get player job/group
function vRPIntegration.getPlayerJob(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local job = vRP.getUserGroupByType({user_id, "job"}) or "unemployed"
        local jobGrade = vRP.getUserGroupByType({user_id, "job"}) or 0
        
        return {
            name = job,
            label = job:gsub("^%l", string.upper), -- Capitalize first letter
            grade = jobGrade,
            grade_name = job,
            grade_label = job:gsub("^%l", string.upper)
        }
    end
    return {
        name = "unemployed",
        label = "عاطل",
        grade = 0,
        grade_name = "unemployed",
        grade_label = "عاطل"
    }
end

-- Check if player has permission
function vRPIntegration.hasPermission(source, permission)
    local user_id = vRP.getUserId({source})
    if user_id then
        return vRP.hasPermission({user_id, permission})
    end
    return false
end

-- Check if player has group
function vRPIntegration.hasGroup(source, group)
    local user_id = vRP.getUserId({source})
    if user_id then
        return vRP.hasGroup({user_id, group})
    end
    return false
end

-- Get player groups
function vRPIntegration.getPlayerGroups(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        return vRP.getUserGroups({user_id}) or {}
    end
    return {}
end

-- Add money to player
function vRPIntegration.addMoney(source, amount, account)
    local user_id = vRP.getUserId({source})
    if user_id then
        if account == "bank" then
            vRP.giveBankMoney({user_id, amount})
        else
            vRP.giveMoney({user_id, amount})
        end
        return true
    end
    return false
end

-- Remove money from player
function vRPIntegration.removeMoney(source, amount, account)
    local user_id = vRP.getUserId({source})
    if user_id then
        if account == "bank" then
            if vRP.getBankMoney({user_id}) >= amount then
                vRP.setBankMoney({user_id, vRP.getBankMoney({user_id}) - amount})
                return true
            end
        else
            if vRP.getMoney({user_id}) >= amount then
                vRP.setMoney({user_id, vRP.getMoney({user_id}) - amount})
                return true
            end
        end
    end
    return false
end

-- Get player inventory
function vRPIntegration.getPlayerInventory(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local inventory = vRP.getInventory({user_id}) or {}
        local formattedInventory = {}
        
        for item, data in pairs(inventory) do
            table.insert(formattedInventory, {
                name = item,
                label = vRP.getItemName({item}) or item,
                count = data.amount or 0,
                weight = vRP.getItemWeight({item}) or 0
            })
        end
        
        return formattedInventory
    end
    return {}
end

-- Give item to player
function vRPIntegration.giveItem(source, item, amount)
    local user_id = vRP.getUserId({source})
    if user_id then
        return vRP.giveInventoryItem({user_id, item, amount, true})
    end
    return false
end

-- Remove item from player
function vRPIntegration.removeItem(source, item, amount)
    local user_id = vRP.getUserId({source})
    if user_id then
        if vRP.getInventoryItemAmount({user_id, item}) >= amount then
            vRP.tryGetInventoryItem({user_id, item, amount, true})
            return true
        end
    end
    return false
end

-- Get player vehicles
function vRPIntegration.getPlayerVehicles(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local vehicles = vRP.getUserVehicles({user_id}) or {}
        local formattedVehicles = {}
        
        for vehicle, data in pairs(vehicles) do
            table.insert(formattedVehicles, {
                model = vehicle,
                plate = data.plate or "UNKNOWN",
                stored = data.stored or 1,
                garage = data.garage or "default"
            })
        end
        
        return formattedVehicles
    end
    return {}
end

-- Get player apartments/homes
function vRPIntegration.getPlayerHomes(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local homes = vRP.getUserHomes({user_id}) or {}
        local formattedHomes = {}
        
        for home, data in pairs(homes) do
            table.insert(formattedHomes, {
                name = home,
                number = data.number or 0,
                x = data.x or 0,
                y = data.y or 0,
                z = data.z or 0
            })
        end
        
        return formattedHomes
    end
    return {}
end

-- Kick player
function vRPIntegration.kickPlayer(source, reason)
    local user_id = vRP.getUserId({source})
    if user_id then
        vRP.kick({user_id, reason or "تم طردك من السيرفر"})
        return true
    end
    return false
end

-- Ban player
function vRPIntegration.banPlayer(source, reason, duration)
    local user_id = vRP.getUserId({source})
    if user_id then
        if duration and duration > 0 then
            vRP.ban({user_id, reason or "تم حظرك من السيرفر", duration})
        else
            vRP.ban({user_id, reason or "تم حظرك من السيرفر"})
        end
        return true
    end
    return false
end

-- Unban player
function vRPIntegration.unbanPlayer(user_id)
    if user_id then
        vRP.unban({user_id})
        return true
    end
    return false
end

-- Send notification to player
function vRPIntegration.notify(source, message, type)
    local user_id = vRP.getUserId({source})
    if user_id then
        if type == "error" then
            vRPclient.notify(source, {message})
        elseif type == "success" then
            vRPclient.notify(source, {message})
        else
            vRPclient.notify(source, {message})
        end
    end
end

-- Teleport player
function vRPIntegration.teleportPlayer(source, x, y, z)
    local user_id = vRP.getUserId({source})
    if user_id then
        vRPclient.teleport(source, {x, y, z})
        return true
    end
    return false
end

-- Get all online players
function vRPIntegration.getOnlinePlayers()
    local players = {}
    local users = vRP.getUsers({})
    
    for user_id, source in pairs(users) do
        if source then
            table.insert(players, {
                user_id = user_id,
                source = source,
                name = vRP.getPlayerName({user_id}) or GetPlayerName(source),
                group = vRP.getUserGroupByType({user_id, "job"}) or "user"
            })
        end
    end
    
    return players
end

-- Get player data for menu
function vRPIntegration.getMenuPlayerData(source)
    local user_id = vRP.getUserId({source})
    if not user_id then return nil end
    
    local money = vRPIntegration.getPlayerMoney(source)
    local job = vRPIntegration.getPlayerJob(source)
    local inventory = vRPIntegration.getPlayerInventory(source)
    local vehicles = vRPIntegration.getPlayerVehicles(source)
    local homes = vRPIntegration.getPlayerHomes(source)
    local groups = vRPIntegration.getPlayerGroups(source)
    
    return {
        user_id = user_id,
        name = vRPIntegration.getPlayerName(source),
        money = money,
        job = job,
        inventory = inventory,
        vehicles = vehicles,
        homes = homes,
        groups = groups,
        isAdmin = vRPIntegration.hasGroup(source, "admin") or vRPIntegration.hasGroup(source, "superadmin")
    }
end

-- Export the integration
_G.vRPIntegration = vRPIntegration
return vRPIntegration
