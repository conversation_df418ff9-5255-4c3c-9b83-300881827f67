/*! Chart.js v3.9.1 | (c) Chart.js Contributors | chartjs.org/license */
/* This is a placeholder for Chart.js - you should include the actual Chart.js library */
/* Download from: https://www.chartjs.org/docs/latest/getting-started/installation.html */

// Minimal Chart.js-like functionality for basic charts
(function(global) {
    'use strict';
    
    function Chart(ctx, config) {
        this.ctx = typeof ctx === 'string' ? document.getElementById(ctx) : ctx;
        this.config = config;
        this.data = config.data || {};
        this.options = config.options || {};
        this.type = config.type || 'line';
        
        this.init();
    }
    
    Chart.prototype = {
        init: function() {
            if (!this.ctx) return;
            
            // Create canvas if element is not canvas
            if (this.ctx.tagName !== 'CANVAS') {
                const canvas = document.createElement('canvas');
                canvas.width = this.ctx.offsetWidth || 400;
                canvas.height = this.ctx.offsetHeight || 200;
                this.ctx.appendChild(canvas);
                this.ctx = canvas;
            }
            
            this.context = this.ctx.getContext('2d');
            this.draw();
        },
        
        draw: function() {
            if (!this.context) return;
            
            const ctx = this.context;
            const width = this.ctx.width;
            const height = this.ctx.height;
            
            // Clear canvas
            ctx.clearRect(0, 0, width, height);
            
            if (this.type === 'line') {
                this.drawLineChart();
            } else if (this.type === 'bar') {
                this.drawBarChart();
            } else if (this.type === 'doughnut' || this.type === 'pie') {
                this.drawDoughnutChart();
            }
        },
        
        drawLineChart: function() {
            const ctx = this.context;
            const width = this.ctx.width;
            const height = this.ctx.height;
            const padding = 40;
            
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            
            if (!this.data.datasets || !this.data.datasets[0] || !this.data.datasets[0].data) return;
            
            const dataset = this.data.datasets[0];
            const data = dataset.data;
            const labels = this.data.labels || [];
            
            if (data.length === 0) return;
            
            // Find min and max values
            const maxValue = Math.max(...data, 0);
            const minValue = Math.min(...data, 0);
            const range = maxValue - minValue || 1;
            
            // Draw grid
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            
            // Vertical grid lines
            for (let i = 0; i <= 5; i++) {
                const x = padding + (chartWidth / 5) * i;
                ctx.beginPath();
                ctx.moveTo(x, padding);
                ctx.lineTo(x, height - padding);
                ctx.stroke();
            }
            
            // Horizontal grid lines
            for (let i = 0; i <= 5; i++) {
                const y = padding + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();
            }
            
            // Draw line
            if (data.length > 1) {
                ctx.strokeStyle = dataset.borderColor || '#00d4ff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let i = 0; i < data.length; i++) {
                    const x = padding + (chartWidth / (data.length - 1)) * i;
                    const y = height - padding - ((data[i] - minValue) / range) * chartHeight;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
                
                // Fill area if specified
                if (dataset.fill && dataset.backgroundColor) {
                    ctx.fillStyle = dataset.backgroundColor;
                    ctx.lineTo(width - padding, height - padding);
                    ctx.lineTo(padding, height - padding);
                    ctx.closePath();
                    ctx.fill();
                }
            }
            
            // Draw points
            ctx.fillStyle = dataset.borderColor || '#00d4ff';
            for (let i = 0; i < data.length; i++) {
                const x = padding + (chartWidth / (data.length - 1)) * i;
                const y = height - padding - ((data[i] - minValue) / range) * chartHeight;
                
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            }
        },
        
        drawBarChart: function() {
            const ctx = this.context;
            const width = this.ctx.width;
            const height = this.ctx.height;
            const padding = 40;
            
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            
            if (!this.data.datasets || !this.data.datasets[0] || !this.data.datasets[0].data) return;
            
            const dataset = this.data.datasets[0];
            const data = dataset.data;
            
            if (data.length === 0) return;
            
            const maxValue = Math.max(...data, 0);
            const barWidth = chartWidth / data.length * 0.8;
            const barSpacing = chartWidth / data.length * 0.2;
            
            ctx.fillStyle = dataset.backgroundColor || '#00d4ff';
            
            for (let i = 0; i < data.length; i++) {
                const barHeight = (data[i] / maxValue) * chartHeight;
                const x = padding + i * (barWidth + barSpacing) + barSpacing / 2;
                const y = height - padding - barHeight;
                
                ctx.fillRect(x, y, barWidth, barHeight);
            }
        },
        
        drawDoughnutChart: function() {
            const ctx = this.context;
            const width = this.ctx.width;
            const height = this.ctx.height;
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = Math.min(width, height) / 2 - 20;
            
            if (!this.data.datasets || !this.data.datasets[0] || !this.data.datasets[0].data) return;
            
            const dataset = this.data.datasets[0];
            const data = dataset.data;
            const colors = dataset.backgroundColor || ['#ff6384', '#36a2eb', '#ffce56', '#4bc0c0'];
            
            if (data.length === 0) return;
            
            const total = data.reduce((sum, value) => sum + value, 0);
            let currentAngle = -Math.PI / 2;
            
            for (let i = 0; i < data.length; i++) {
                const sliceAngle = (data[i] / total) * 2 * Math.PI;
                
                ctx.fillStyle = colors[i % colors.length];
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                
                if (this.type === 'doughnut') {
                    ctx.arc(centerX, centerY, radius * 0.6, currentAngle + sliceAngle, currentAngle, true);
                } else {
                    ctx.lineTo(centerX, centerY);
                }
                
                ctx.closePath();
                ctx.fill();
                
                currentAngle += sliceAngle;
            }
        },
        
        update: function(mode) {
            this.draw();
        },
        
        destroy: function() {
            if (this.context) {
                this.context.clearRect(0, 0, this.ctx.width, this.ctx.height);
            }
        }
    };
    
    // Chart types
    Chart.defaults = {
        global: {
            responsive: true,
            maintainAspectRatio: true
        }
    };
    
    // Export to global
    global.Chart = Chart;
    
})(window);
