-- CB Pause Menu - Error Checking Script
-- Run this to check for common issues

print("^3[CB_PAUSEMENU]^7 Starting error check...")

-- Check 1: Config file
if Config then
    print("^2[✓]^7 Config loaded successfully")
    
    if Config.Framework then
        print("^2[✓]^7 Framework set to: " .. Config.Framework)
    else
        print("^1[✗]^7 Framework not set in config")
    end
    
    if Config.Locale then
        print("^2[✓]^7 Locale set to: " .. Config.Locale)
    else
        print("^1[✗]^7 Locale not set in config")
    end
else
    print("^1[✗]^7 Config not loaded - check config.lua")
end

-- Check 2: Required functions
local requiredFunctions = {
    "RegisterCommand",
    "RegisterKeyMapping", 
    "AddEventHandler",
    "TriggerEvent",
    "TriggerServerEvent",
    "GetPlayerName",
    "GetPlayerServerId"
}

for _, func in pairs(requiredFunctions) do
    if _G[func] then
        print("^2[✓]^7 Function available: " .. func)
    else
        print("^1[✗]^7 Function missing: " .. func)
    end
end

-- Check 3: File structure (server-side only)
if IsDuplicityVersion() then
    print("^3[INFO]^7 Running server-side checks...")
    
    -- Check resource files
    local requiredFiles = {
        "fxmanifest.lua",
        "config.lua",
        "html/index.html",
        "html/style.css",
        "html/script.js"
    }
    
    for _, file in pairs(requiredFiles) do
        local path = GetResourcePath(GetCurrentResourceName()) .. "/" .. file
        -- Note: LoadResourceFile would be better but this is a basic check
        print("^3[INFO]^7 Should have file: " .. file)
    end
    
    -- Check players
    local players = GetPlayers()
    print("^2[✓]^7 Current players online: " .. #players)
    
else
    print("^3[INFO]^7 Running client-side checks...")
    
    -- Check player state
    if NetworkIsPlayerActive(PlayerId()) then
        print("^2[✓]^7 Player is active")
        print("^2[✓]^7 Player ID: " .. PlayerId())
        print("^2[✓]^7 Server ID: " .. GetPlayerServerId(PlayerId()))
        print("^2[✓]^7 Player Name: " .. GetPlayerName(PlayerId()))
    else
        print("^1[✗]^7 Player not active yet")
    end
    
    -- Check controls
    if IsControlEnabled(0, 322) then -- ESC key
        print("^2[✓]^7 ESC key control available")
    else
        print("^1[✗]^7 ESC key control disabled")
    end
end

-- Check 4: Events
print("^3[INFO]^7 Registering test events...")

if IsDuplicityVersion() then
    -- Server events
    RegisterServerEvent('cb_pausemenu:errorCheck')
    AddEventHandler('cb_pausemenu:errorCheck', function()
        print("^2[✓]^7 Server event system working")
        TriggerClientEvent('cb_pausemenu:errorCheckResponse', source, true)
    end)
else
    -- Client events  
    RegisterNetEvent('cb_pausemenu:errorCheckResponse')
    AddEventHandler('cb_pausemenu:errorCheckResponse', function(success)
        if success then
            print("^2[✓]^7 Client-Server communication working")
        else
            print("^1[✗]^7 Client-Server communication failed")
        end
    end)
    
    -- Test server communication
    Citizen.SetTimeout(2000, function()
        TriggerServerEvent('cb_pausemenu:errorCheck')
    end)
end

-- Check 5: NUI (client-side only)
if not IsDuplicityVersion() then
    Citizen.SetTimeout(3000, function()
        print("^3[INFO]^7 Testing NUI...")
        
        -- Test NUI message
        SendNUIMessage({
            type = 'test',
            message = 'Error check test'
        })
        
        -- Register test callback
        RegisterNUICallback('errorCheckTest', function(data, cb)
            print("^2[✓]^7 NUI communication working")
            cb('ok')
        end)
    end)
end

-- Check 6: Commands
RegisterCommand('cbcheck', function(source, args)
    local playerId = source or PlayerId()
    local playerName = GetPlayerName(playerId)
    
    print("^3[CB_PAUSEMENU]^7 Manual check triggered by: " .. playerName)
    print("^2[✓]^7 Command system working")
    
    if not IsDuplicityVersion() then
        TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'CB Pause Menu check completed - see console')
    end
end, false)

-- Final status
Citizen.SetTimeout(5000, function()
    print("^3[CB_PAUSEMENU]^7 Error check completed!")
    print("^3[INFO]^7 Use /cbcheck command to run manual check")
    print("^3[INFO]^7 Check console above for any ^1[✗]^7 errors")
end)

-- Export check function
if IsDuplicityVersion() then
    exports('checkErrors', function()
        print("^3[CB_PAUSEMENU]^7 Running exported error check...")
        return true
    end)
end
