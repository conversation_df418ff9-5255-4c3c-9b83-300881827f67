-- Migration Script: ESX to vRP for CB Pause Menu
-- This script helps migrate data from ESX format to vRP format

-- Backup existing data before running this script
-- CREATE TABLE cb_pausemenu_data_backup AS SELECT * FROM cb_pausemenu_data;

-- Step 1: Create temporary table for identifier mapping
CREATE TEMPORARY TABLE identifier_mapping (
    esx_identifier VARCHAR(50),
    vrp_user_id INT,
    steam_id VARCHAR(50),
    license_id VARCHAR(50)
);

-- Step 2: Populate mapping table (you need to adjust this based on your vRP users table)
-- This assumes you have a vrp_users table with steam and license identifiers
INSERT INTO identifier_mapping (esx_identifier, vrp_user_id, steam_id, license_id)
SELECT 
    CONCAT('steam:', u.steam) as esx_identifier,
    u.id as vrp_user_id,
    u.steam as steam_id,
    u.license as license_id
FROM vrp_users u
WHERE u.steam IS NOT NULL;

-- Alternative mapping for license-based identifiers
INSERT INTO identifier_mapping (esx_identifier, vrp_user_id, steam_id, license_id)
SELECT 
    CONCAT('license:', u.license) as esx_identifier,
    u.id as vrp_user_id,
    u.steam as steam_id,
    u.license as license_id
FROM vrp_users u
WHERE u.license IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM identifier_mapping im 
    WHERE im.vrp_user_id = u.id
);

-- Step 3: Update CB Pause Menu data with vRP user_ids
UPDATE cb_pausemenu_data cbd
JOIN identifier_mapping im ON cbd.identifier = im.esx_identifier
SET cbd.identifier = im.vrp_user_id;

-- Step 4: Update logs table
UPDATE cb_pausemenu_logs cbl
JOIN identifier_mapping im ON cbl.identifier = im.esx_identifier
SET cbl.identifier = im.vrp_user_id;

-- Step 5: Update bans table
UPDATE cb_pausemenu_bans cbb
JOIN identifier_mapping im ON cbb.identifier = im.esx_identifier
SET cbb.identifier = im.vrp_user_id;

-- Step 6: Update bug reports table
UPDATE cb_pausemenu_bug_reports cbbr
JOIN identifier_mapping im ON cbbr.identifier = im.esx_identifier
SET cbbr.identifier = im.vrp_user_id;

-- Step 7: Update reports table
UPDATE cb_pausemenu_reports cbr
JOIN identifier_mapping im ON cbr.reporter_id = im.esx_identifier
SET cbr.reporter_id = im.vrp_user_id;

-- Step 8: Update friends table
UPDATE cb_pausemenu_friends cbf
JOIN identifier_mapping im1 ON cbf.player1_id = im1.esx_identifier
JOIN identifier_mapping im2 ON cbf.player2_id = im2.esx_identifier
SET cbf.player1_id = im1.vrp_user_id,
    cbf.player2_id = im2.vrp_user_id;

-- Step 9: Update achievements table
UPDATE cb_pausemenu_achievements cba
JOIN identifier_mapping im ON cba.identifier = im.esx_identifier
SET cba.identifier = im.vrp_user_id;

-- Step 10: Update VIP table
UPDATE cb_pausemenu_vip cbv
JOIN identifier_mapping im ON cbv.identifier = im.esx_identifier
SET cbv.identifier = im.vrp_user_id;

-- Step 11: Clean up orphaned records (optional)
-- Remove records that couldn't be mapped
DELETE FROM cb_pausemenu_data 
WHERE identifier NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_logs 
WHERE identifier NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_bans 
WHERE identifier NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_bug_reports 
WHERE identifier NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_reports 
WHERE reporter_id NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_friends 
WHERE player1_id NOT REGEXP '^[0-9]+$' 
   OR player2_id NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_achievements 
WHERE identifier NOT REGEXP '^[0-9]+$';

DELETE FROM cb_pausemenu_vip 
WHERE identifier NOT REGEXP '^[0-9]+$';

-- Step 12: Update data format for vRP compatibility
-- Convert ESX job format to vRP group format
UPDATE cb_pausemenu_data 
SET data = JSON_SET(
    data, 
    '$.job', 
    JSON_OBJECT(
        'name', JSON_UNQUOTE(JSON_EXTRACT(data, '$.job.name')),
        'label', JSON_UNQUOTE(JSON_EXTRACT(data, '$.job.label')),
        'grade', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.job.grade')), 0)
    )
)
WHERE JSON_VALID(data) = 1 
AND JSON_EXTRACT(data, '$.job') IS NOT NULL;

-- Convert ESX money format to vRP money format
UPDATE cb_pausemenu_data 
SET data = JSON_SET(
    data,
    '$.money',
    JSON_OBJECT(
        'wallet', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.money.cash')), 0),
        'bank', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.money.bank')), 0)
    )
)
WHERE JSON_VALID(data) = 1 
AND JSON_EXTRACT(data, '$.money') IS NOT NULL;

-- Step 13: Create verification queries
-- Check migration results
SELECT 
    'cb_pausemenu_data' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN identifier REGEXP '^[0-9]+$' THEN 1 END) as vrp_format_records
FROM cb_pausemenu_data

UNION ALL

SELECT 
    'cb_pausemenu_logs' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN identifier REGEXP '^[0-9]+$' THEN 1 END) as vrp_format_records
FROM cb_pausemenu_logs

UNION ALL

SELECT 
    'cb_pausemenu_bans' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN identifier REGEXP '^[0-9]+$' THEN 1 END) as vrp_format_records
FROM cb_pausemenu_bans;

-- Step 14: Create indexes for better performance with vRP
CREATE INDEX idx_cb_pausemenu_data_vrp_user_id ON cb_pausemenu_data(identifier);
CREATE INDEX idx_cb_pausemenu_logs_vrp_user_id ON cb_pausemenu_logs(identifier);
CREATE INDEX idx_cb_pausemenu_bans_vrp_user_id ON cb_pausemenu_bans(identifier);

-- Step 15: Update stored procedures for vRP
DROP PROCEDURE IF EXISTS GetPlayerData;
DELIMITER //
CREATE PROCEDURE GetPlayerData(IN player_user_id INT)
BEGIN
    SELECT * FROM cb_pausemenu_data WHERE identifier = player_user_id;
END //
DELIMITER ;

DROP PROCEDURE IF EXISTS UpdatePlayerPlaytime;
DELIMITER //
CREATE PROCEDURE UpdatePlayerPlaytime(IN player_user_id INT, IN new_playtime DECIMAL(10,2))
BEGIN
    UPDATE cb_pausemenu_data 
    SET data = JSON_SET(data, '$.playtime', new_playtime)
    WHERE identifier = player_user_id;
END //
DELIMITER ;

-- Step 16: Update views for vRP
DROP VIEW IF EXISTS v_player_stats;
CREATE VIEW v_player_stats AS
SELECT 
    identifier as user_id,
    JSON_UNQUOTE(JSON_EXTRACT(data, '$.playtime')) as playtime,
    JSON_UNQUOTE(JSON_EXTRACT(data, '$.rank')) as rank,
    JSON_UNQUOTE(JSON_EXTRACT(data, '$.lastSeen')) as last_seen,
    updated_at
FROM cb_pausemenu_data;

-- Migration completion message
SELECT 'Migration from ESX to vRP completed successfully!' as status;

-- Cleanup temporary table
DROP TEMPORARY TABLE identifier_mapping;
