-- CB Pause Menu - Simple Client Script for Testing

local isMenuOpen = false
local playerData = {}
local currentFPS = 0
local playtime = 0

-- Wait for player to spawn
Citizen.CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Citizen.Wait(0)
    end
    
    -- Wait a bit more for everything to load
    Citizen.Wait(2000)
    
    -- Initialize player data
    playerData = {
        name = GetPlayerName(PlayerId()),
        id = GetPlayerServerId(PlayerId()),
        money = 5000,
        bank = 25000,
        job = {name = "unemployed", label = "عاطل"},
        playtime = 0
    }
    
    print("^2[CB_PAUSEMENU]^7 Client loaded successfully!")
end)

-- Main thread for FPS monitoring
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Update every second
        currentFPS = math.floor(1.0 / GetFrameTime())

        if isMenuOpen then
            SendNUIMessage({
                type = 'updateFPS',
                fps = currentFPS
            })
        end
    end
end)

-- Register key mapping
Citizen.CreateThread(function()
    RegisterKeyMapping('pausemenu', 'Open Pause Menu', 'keyboard', 'ESCAPE')
end)

-- Command to open menu
RegisterCommand('pausemenu', function()
    ToggleMenu()
end, false)

-- ESC key handler
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if IsControlJustPressed(0, 322) then -- ESC key
            if not isMenuOpen then
                ToggleMenu()
            end
        end
    end
end)

-- Function to toggle menu
function ToggleMenu()
    if isMenuOpen then
        CloseMenu()
    else
        OpenMenu()
    end
end

-- Function to open menu
function OpenMenu()
    if isMenuOpen then return end
    
    isMenuOpen = true
    SetNuiFocus(true, true)
    
    -- Get player data
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)
    
    -- Calculate rank
    local playerRank = CalculatePlayerRank(playtime)

    -- Send data to NUI
    SendNUIMessage({
        type = 'openMenu',
        data = {
            playerName = playerData.name or GetPlayerName(PlayerId()),
            playerId = playerData.id or GetPlayerServerId(PlayerId()),
            playtime = FormatPlaytime(playtime),
            rank = playerRank,
            money = playerData.money or 0,
            bank = playerData.bank or 0,
            job = playerData.job or {name = 'unemployed', label = 'عاطل'},
            fps = currentFPS,
            coords = {x = playerCoords.x, y = playerCoords.y, z = playerCoords.z},
            heading = playerHeading,
            serverName = "CB STORE SERVER",
            maxPlayers = 64,
            currentPlayers = #GetActivePlayers(),
            discordAvatar = "https://cdn.discordapp.com/embed/avatars/0.png",
            config = {
                Ranks = {
                    {name = 'مبتدئ', minPlaytime = 0, color = '#808080'},
                    {name = 'لاعب', minPlaytime = 10, color = '#00FF00'},
                    {name = 'محترف', minPlaytime = 50, color = '#0080FF'},
                    {name = 'خبير', minPlaytime = 100, color = '#8000FF'},
                    {name = 'أسطورة', minPlaytime = 200, color = '#FF8000'},
                    {name = 'بطل', minPlaytime = 500, color = '#FF0000'}
                }
            }
        }
    })
    
    -- Disable controls
    DisableControlsWhileMenuOpen()
end

-- Function to close menu
function CloseMenu()
    if not isMenuOpen then return end
    
    isMenuOpen = false
    SetNuiFocus(false, false)
    
    SendNUIMessage({
        type = 'closeMenu'
    })
end

-- Function to calculate player rank
function CalculatePlayerRank(playtimeHours)
    local ranks = {
        {name = 'مبتدئ', minPlaytime = 0, color = '#808080'},
        {name = 'لاعب', minPlaytime = 10, color = '#00FF00'},
        {name = 'محترف', minPlaytime = 50, color = '#0080FF'},
        {name = 'خبير', minPlaytime = 100, color = '#8000FF'},
        {name = 'أسطورة', minPlaytime = 200, color = '#FF8000'},
        {name = 'بطل', minPlaytime = 500, color = '#FF0000'}
    }

    local rank = ranks[1] -- Default to first rank

    for i = #ranks, 1, -1 do
        if playtimeHours >= ranks[i].minPlaytime then
            rank = ranks[i]
            break
        end
    end

    return rank
end

-- Function to format playtime
function FormatPlaytime(hours)
    if hours < 1 then
        return math.floor(hours * 60) .. " دقيقة"
    elseif hours < 24 then
        return math.floor(hours) .. " ساعة"
    else
        local days = math.floor(hours / 24)
        local remainingHours = math.floor(hours % 24)
        return days .. " يوم " .. remainingHours .. " ساعة"
    end
end

-- Function to disable controls while menu is open
function DisableControlsWhileMenuOpen()
    Citizen.CreateThread(function()
        while isMenuOpen do
            Citizen.Wait(0)
            
            -- Disable all controls
            DisableAllControlActions(0)
            
            -- Enable cursor controls
            EnableControlAction(0, 1, true) -- LookLeftRight
            EnableControlAction(0, 2, true) -- LookUpDown
            EnableControlAction(0, 142, true) -- MeleeAttackAlternate
            EnableControlAction(0, 18, true) -- Enter
            EnableControlAction(0, 322, true) -- ESC
            EnableControlAction(0, 106, true) -- VehicleMouseControlOverride
        end
    end)
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(_, cb)
    CloseMenu()
    cb('ok')
end)

RegisterNUICallback('disconnect', function(_, cb)
    TriggerServerEvent('cb_pausemenu:disconnect')
    cb('ok')
end)

RegisterNUICallback('teleportToWaypoint', function(_, cb)
    TeleportToWaypoint()
    cb('ok')
end)

RegisterNUICallback('takeScreenshot', function(_, cb)
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم أخذ لقطة الشاشة')
    cb('ok')
end)

-- Function to teleport to waypoint
function TeleportToWaypoint()
    local waypoint = GetFirstBlipInfoId(8)
    if DoesBlipExist(waypoint) then
        local coords = GetBlipInfoIdCoord(waypoint)
        local ground, z = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z, false)
        
        if ground then
            coords.z = z
        end
        
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)
        TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم النقل إلى النقطة المحددة')
    else
        TriggerEvent('chatMessage', 'SYSTEM', {255, 0, 0}, 'لم يتم تحديد نقطة على الخريطة')
    end
end

-- Events for updating data
RegisterNetEvent('cb_pausemenu:updatePlayerData')
AddEventHandler('cb_pausemenu:updatePlayerData', function(data)
    if data then
        playerData = data
        playtime = data.playtime or 0
        
        if isMenuOpen then
            SendNUIMessage({
                type = 'updatePlayerData',
                data = data
            })
        end
    end
end)

-- Test command to add playtime
RegisterCommand('addplaytime', function(_, args)
    local hours = tonumber(args[1]) or 1
    playtime = playtime + hours
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم إضافة ' .. hours .. ' ساعة لوقت اللعب')
end, false)

-- Test command to add money
RegisterCommand('addmoney', function(_, args)
    local amount = tonumber(args[1]) or 1000
    playerData.money = (playerData.money or 0) + amount
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم إضافة $' .. amount .. ' للمال')
end, false)

print("^2[CB_PAUSEMENU]^7 Simple client script loaded!")
