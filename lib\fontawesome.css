/* FontAwesome Icons for CB Pause Menu */
/* This is a minimal version - you should include the full FontAwesome CSS */

@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

/* Custom icon styles for the menu */
.fa-custom {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

/* Icon sizes */
.fa-xs {
    font-size: 0.75em;
}

.fa-sm {
    font-size: 0.875em;
}

.fa-lg {
    font-size: 1.25em;
}

.fa-xl {
    font-size: 1.5em;
}

.fa-2xl {
    font-size: 2em;
}

/* Icon colors for menu */
.icon-primary {
    color: #00d4ff;
}

.icon-secondary {
    color: #6c757d;
}

.icon-success {
    color: #28a745;
}

.icon-danger {
    color: #dc3545;
}

.icon-warning {
    color: #ffc107;
}

.icon-info {
    color: #17a2b8;
}

.icon-light {
    color: #f8f9fa;
}

.icon-dark {
    color: #343a40;
}

/* Icon animations */
.icon-spin {
    animation: fa-spin 2s infinite linear;
}

.icon-pulse {
    animation: fa-pulse 2s infinite;
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fa-pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.25;
    }
    100% {
        opacity: 1;
    }
}

/* Custom menu icons */
.menu-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 16px;
}

.menu-icon.large {
    width: 32px;
    height: 32px;
    font-size: 20px;
}

/* Icon hover effects */
.icon-hover:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.icon-glow:hover {
    text-shadow: 0 0 10px currentColor;
    transition: text-shadow 0.3s ease;
}

/* Status icons */
.status-online::before {
    content: "\f111";
    color: #28a745;
}

.status-offline::before {
    content: "\f111";
    color: #6c757d;
}

.status-away::before {
    content: "\f111";
    color: #ffc107;
}

.status-busy::before {
    content: "\f111";
    color: #dc3545;
}

/* Navigation icons */
.nav-icon {
    margin-left: 8px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.nav-item:hover .nav-icon,
.nav-item.active .nav-icon {
    opacity: 1;
}

/* Button icons */
.btn-icon {
    margin-left: 8px;
}

.btn-icon-only {
    margin: 0;
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    animation: fa-spin 1s infinite linear;
}

/* Icon badges */
.icon-badge {
    position: relative;
}

.icon-badge::after {
    content: attr(data-badge);
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Icon groups */
.icon-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.icon-group .icon {
    flex-shrink: 0;
}

/* Responsive icons */
@media (max-width: 768px) {
    .menu-icon {
        width: 20px;
        height: 20px;
        font-size: 14px;
    }
    
    .menu-icon.large {
        width: 28px;
        height: 28px;
        font-size: 18px;
    }
}
