@echo off
color 0C
echo.
echo ========================================
echo   FIX BLURRED SCREEN - INSTANT SOLUTION
echo ========================================
echo.
echo I can see you have the blurred screen problem!
echo This will fix it in 30 seconds.
echo.
pause

echo.
echo [STEP 1] Backing up problematic files...
if exist html\index.html (
    ren html\index.html index.html.broken >nul 2>&1
    echo - Moved broken index.html
)
if exist client.lua (
    ren client.lua client.lua.broken >nul 2>&1
    echo - Moved broken client.lua
)

echo.
echo [STEP 2] Creating working HTML file...
mkdir html >nul 2>&1

(
echo ^<!DOCTYPE html^>
echo ^<html^>
echo ^<head^>
echo ^<meta charset="UTF-8"^>
echo ^<title^>CB Menu^</title^>
echo ^<style^>
echo body { background: transparent; color: white; font-family: Arial; margin: 0; padding: 0; }
echo .menu { display: none; position: fixed; top: 50%%; left: 50%%; transform: translate^(-50%%, -50%%^); background: linear-gradient^(135deg, #1a1a2e, #16213e^); padding: 30px; border-radius: 15px; border: 2px solid #00d4ff; box-shadow: 0 20px 60px rgba^(0,0,0,0.8^); }
echo .menu.show { display: block; }
echo h1 { color: #00d4ff; text-align: center; margin-bottom: 20px; text-shadow: 0 0 10px rgba^(0,212,255,0.5^); }
echo .info { margin: 15px 0; padding: 10px; background: rgba^(255,255,255,0.1^); border-radius: 8px; display: flex; justify-content: space-between; }
echo .label { color: #ccc; }
echo .value { color: #fff; font-weight: bold; }
echo .btn { background: linear-gradient^(45deg, #00d4ff, #0099cc^); color: white; border: none; padding: 12px 24px; margin: 8px; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s; }
echo .btn:hover { transform: translateY^(-2px^); box-shadow: 0 5px 15px rgba^(0,212,255,0.4^); }
echo .btn.danger { background: linear-gradient^(45deg, #ff4757, #ff3742^); }
echo .close { position: absolute; top: 10px; right: 15px; background: rgba^(255,0,0,0.8^); border: none; border-radius: 50%%; width: 35px; height: 35px; color: white; font-size: 18px; cursor: pointer; }
echo ^</style^>
echo ^</head^>
echo ^<body^>
echo ^<div class="menu" id="menu"^>
echo ^<button class="close" onclick="closeMenu()"^>×^</button^>
echo ^<h1^>CB STORE SERVER^</h1^>
echo ^<div class="info"^>^<span class="label"^>اسم اللاعب:^</span^>^<span class="value" id="name"^>Player^</span^>^</div^>
echo ^<div class="info"^>^<span class="label"^>المال:^</span^>^<span class="value" id="money"^>$5000^</span^>^</div^>
echo ^<div class="info"^>^<span class="label"^>وقت اللعب:^</span^>^<span class="value" id="time"^>0 ساعة^</span^>^</div^>
echo ^<div class="info"^>^<span class="label"^>FPS:^</span^>^<span class="value" id="fps"^>60^</span^>^</div^>
echo ^<div style="text-align: center; margin-top: 20px;"^>
echo ^<button class="btn" onclick="takeScreenshot()"^>لقطة شاشة^</button^>
echo ^<button class="btn" onclick="teleport()"^>النقل^</button^>
echo ^<button class="btn danger" onclick="disconnect()"^>قطع الاتصال^</button^>
echo ^<button class="btn" onclick="closeMenu()"^>إغلاق^</button^>
echo ^</div^>
echo ^</div^>
echo ^<script^>
echo let isOpen = false;
echo window.addEventListener^('message', function^(e^) {
echo   if ^(e.data.type === 'openMenu'^) {
echo     isOpen = true;
echo     document.getElementById^('menu'^).classList.add^('show'^);
echo     if ^(e.data.data^) {
echo       document.getElementById^('name'^).textContent = e.data.data.playerName ^|^| 'Player';
echo       document.getElementById^('money'^).textContent = '$' + ^(e.data.data.money ^|^| 5000^);
echo       document.getElementById^('time'^).textContent = e.data.data.playtime ^|^| '0 ساعة';
echo       document.getElementById^('fps'^).textContent = e.data.data.fps ^|^| 60;
echo     }
echo   } else if ^(e.data.type === 'closeMenu'^) {
echo     isOpen = false;
echo     document.getElementById^('menu'^).classList.remove^('show'^);
echo   }
echo }^);
echo function closeMenu^(^) {
echo   try {
echo     fetch^('https://' + window.location.hostname + '/closeMenu', { method: 'POST', body: '{}' }^);
echo   } catch^(e^) {}
echo }
echo function disconnect^(^) {
echo   if ^(confirm^('هل تريد قطع الاتصال؟'^)^) {
echo     try {
echo       fetch^('https://' + window.location.hostname + '/disconnect', { method: 'POST', body: '{}' }^);
echo     } catch^(e^) {}
echo   }
echo }
echo function takeScreenshot^(^) {
echo   try {
echo     fetch^('https://' + window.location.hostname + '/takeScreenshot', { method: 'POST', body: '{}' }^);
echo   } catch^(e^) {}
echo }
echo function teleport^(^) {
echo   try {
echo     fetch^('https://' + window.location.hostname + '/teleportToWaypoint', { method: 'POST', body: '{}' }^);
echo   } catch^(e^) {}
echo }
echo document.addEventListener^('keydown', function^(e^) {
echo   if ^(e.key === 'Escape' ^&^& isOpen^) closeMenu^(^);
echo }^);
echo document.addEventListener^('contextmenu', function^(e^) { e.preventDefault^(^); }^);
echo console.log^('CB Pause Menu - Fixed HTML loaded'^);
echo ^</script^>
echo ^</body^>
echo ^</html^>
) > html\index.html

echo.
echo [STEP 3] Installing working client...
copy client_ultra_simple.lua client.lua >nul 2>&1

echo.
echo [STEP 4] Creating simple fxmanifest...
(
echo fx_version 'cerulean'
echo game 'gta5'
echo author 'CB_STORE'
echo description 'CB Pause Menu - Fixed Blurred Screen'
echo version '1.0.0'
echo ui_page 'html/index.html'
echo client_scripts { 'client.lua' }
echo files { 'html/index.html' }
) > fxmanifest.lua

echo.
echo [STEP 5] Verification...
if exist html\index.html (
    echo - HTML file: CREATED
) else (
    echo - HTML file: FAILED
)

if exist client.lua (
    echo - Client file: CREATED
) else (
    echo - Client file: FAILED
)

if exist fxmanifest.lua (
    echo - Manifest file: CREATED
) else (
    echo - Manifest file: FAILED
)

echo.
echo ========================================
echo   BLURRED SCREEN FIXED!
echo ========================================
echo.
echo WHAT WAS FIXED:
echo - Removed problematic CSS/JS libraries
echo - Created ultra-simple HTML interface
echo - Fixed NUI focus management
echo - Added emergency close options
echo.
echo NEXT STEPS:
echo 1. Restart your server
echo 2. Type: /refresh
echo 3. Type: /ensure cb_pausemenu
echo 4. Press ESC in game to test
echo.
echo EMERGENCY COMMANDS:
echo /fixmouse - Fix mouse cursor
echo /forceclosemenu - Force close menu
echo F1 key - Emergency close
echo.
echo The menu should now work perfectly!
echo No more blurred screen!
echo.
pause
