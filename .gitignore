# CB Pause Menu - Git Ignore File

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build outputs
html/*.min.css
html/*.min.js
html/*.map
lib/*.min.css
lib/*.min.js
lib/*.map
dist/
build/

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
server.log
error.log
access.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Server configuration files (contain sensitive data)
server.cfg
database.cfg
config.local.lua

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Temporary files
tmp/
temp/
*.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# FiveM specific
cache/
citizen/
server-data/
resources/[local]/

# Asset files that are too large
html/assets/videos/
html/assets/audio/
html/assets/*.mp4
html/assets/*.avi
html/assets/*.mov
html/assets/*.mp3
html/assets/*.wav
html/assets/*.ogg

# Optimized assets (generated)
html/assets/optimized/

# Documentation build
docs/
jsdoc/

# Test files
test/
tests/
*.test.js
*.spec.js

# Lua compiled files
*.luac

# Archive files
*.zip
*.rar
*.7z
*.tar.gz

# Personal notes and todos
TODO.md
NOTES.md
personal/

# Local development overrides
config.dev.lua
local.lua

# Screenshots and recordings
screenshots/
recordings/
*.png
*.jpg
*.jpeg
*.gif
!html/assets/logo.png
!html/assets/default-avatar.png

# Sensitive configuration
discord.cfg
webhook.cfg
api-keys.cfg

# Performance logs
performance/
benchmarks/

# User data (should not be in version control)
userdata/
playerdata/

# Certificates and keys
*.pem
*.key
*.crt
*.p12

# Local database dumps
*.sql.backup
dump.sql
backup.sql
