# CB Pause Menu - Advanced ESC Menu System

سكربت قائمة ESC متقدم ومتكامل لخوادم FiveM مع واجهة عربية حديثة ومميزات شاملة.

## المميزات الرئيسية

### 🎮 معلومات اللاعب
- عرض اسم اللاعب ومعرفه
- وقت اللعب الإجمالي والحالي
- نظام الرتب التلقائي
- عرض المال النقدي والبنكي
- الوظيفة الحالية
- صورة Discord الشخصية
- الموقع الحالي والإحداثيات

### ⚙️ الإعدادات المتقدمة
- إعدادات العرض (FPS، الخريطة المصغرة، HUD)
- إعدادات الصوت (الصوت العام، الموسيقى، التأثيرات)
- إعدادات الرسوميات
- حفظ الإعدادات تلقائياً

### 📊 مراقبة الأداء
- عرض FPS في الوقت الفعلي
- رسم بياني لمعدل الإطارات
- إحصائيات الشبكة (البينغ، فقدان الحزم)
- معلومات السيرفر

### 🗺️ نظام الخريطة
- عرض الخريطة المصغرة
- النقل للنقطة المحددة
- تحديد النقاط على الخريطة

### 👥 نظام الأصدقاء
- إضافة وإزالة الأصدقاء
- عرض الأصدقاء المتصلين
- إرسال طلبات الصداقة

### 🛡️ نظام الإدارة
- أدوات الإدارة المتقدمة
- نظام البان والكيك
- إرسال الإعلانات
- وضع الصيانة
- نظام التقارير

## التثبيت

### 1. متطلبات النظام
- FiveM Server
- ESX Framework (أو QBCore)
- MySQL Database
- Node.js (اختياري للتطوير)

### 2. خطوات التثبيت

1. **تحميل الملفات:**
   ```bash
   git clone https://github.com/your-repo/cb_pausemenu.git
   cd cb_pausemenu
   ```

2. **إعداد قاعدة البيانات:**
   ```sql
   -- تشغيل ملف database.sql في قاعدة البيانات
   mysql -u username -p database_name < database.sql
   ```

3. **تكوين الإعدادات:**
   - افتح ملف `config.lua`
   - قم بتعديل الإعدادات حسب احتياجاتك
   - أضف Discord Bot Token إذا كنت تريد دعم Discord

4. **إضافة الموارد:**
   ```lua
   -- في server.cfg
   ensure cb_pausemenu
   ```

### 3. الإعدادات الأساسية

```lua
-- config.lua
Config.Framework = 'esx' -- 'esx' أو 'qb'
Config.Locale = 'ar' -- 'ar' أو 'en'
Config.MenuKey = 'ESCAPE' -- مفتاح فتح القائمة
Config.DiscordBotToken = 'YOUR_BOT_TOKEN' -- رمز Discord Bot
Config.DiscordWebhook = 'YOUR_WEBHOOK_URL' -- رابط Discord Webhook
```

## الاستخدام

### فتح القائمة
- اضغط على مفتاح `ESC` لفتح القائمة
- أو استخدم الأمر `/pausemenu`

### التنقل
- استخدم الماوس للتنقل بين الأقسام
- اضغط على العناصر للتفاعل معها
- اضغط `ESC` مرة أخرى لإغلاق القائمة

### الأقسام المتاحة

#### 📋 معلومات اللاعب
- عرض جميع معلومات اللاعب الأساسية
- الإحصائيات والإنجازات
- حالة الشخصية (الصحة، الدرع)

#### ⚙️ الإعدادات
- تخصيص واجهة اللعبة
- إعدادات الصوت والرسوميات
- حفظ التفضيلات

#### 🗺️ الخريطة
- عرض الموقع الحالي
- النقل السريع
- تحديد النقاط

#### 📊 الأداء
- مراقبة FPS
- إحصائيات الشبكة
- معلومات الأداء

#### 🏢 السيرفر
- معلومات السيرفر
- قائمة اللاعبين المتصلين
- الإعلانات

#### 👥 الأصدقاء
- إدارة قائمة الأصدقاء
- الدردشة مع الأصدقاء
- دعوة الأصدقاء

## التخصيص

### تغيير الألوان
```css
/* في html/style.css */
:root {
    --primary-color: #00d4ff;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
}
```

### إضافة أقسام جديدة
```javascript
// في html/script.js
function addCustomSection(sectionData) {
    // كود إضافة القسم الجديد
}
```

### تخصيص الرتب
```lua
-- في config.lua
Config.Ranks = {
    {name = 'مبتدئ', minPlaytime = 0, color = '#808080'},
    {name = 'لاعب', minPlaytime = 10, color = '#00FF00'},
    -- إضافة المزيد من الرتب
}
```

## API للمطورين

### Client Side Events
```lua
-- فتح القائمة
TriggerEvent('cb_pausemenu:openMenu')

-- إغلاق القائمة
TriggerEvent('cb_pausemenu:closeMenu')

-- تحديث بيانات اللاعب
TriggerEvent('cb_pausemenu:updatePlayerData', data)
```

### Server Side Events
```lua
-- الحصول على بيانات اللاعب
TriggerServerEvent('cb_pausemenu:getPlayerData')

-- حفظ الإعدادات
TriggerServerEvent('cb_pausemenu:updateSettings', settings)
```

### Exports
```lua
-- Client
local isMenuOpen = exports.cb_pausemenu:IsMenuOpen()
exports.cb_pausemenu:OpenMenu()
exports.cb_pausemenu:CloseMenu()

-- Server
local playerData = exports.cb_pausemenu:GetPlayerData(source)
exports.cb_pausemenu:UpdatePlayerPlaytime(source, hours)
```

## الدعم والمساعدة

### المشاكل الشائعة

**القائمة لا تفتح:**
- تأكد من أن المورد يعمل بشكل صحيح
- تحقق من وجود أخطاء في console
- تأكد من تثبيت المتطلبات

**البيانات لا تحفظ:**
- تحقق من اتصال قاعدة البيانات
- تأكد من تشغيل ملف database.sql
- تحقق من صلاحيات قاعدة البيانات

**مشاكل Discord:**
- تأكد من صحة Bot Token
- تحقق من صلاحيات البوت
- تأكد من رابط Webhook

### الحصول على المساعدة
- Discord: [رابط السيرفر]
- GitHub Issues: [رابط المشاكل]
- الوثائق: [رابط الوثائق]

## المساهمة

نرحب بمساهماتكم في تطوير المشروع:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

- فريق FiveM للمنصة الرائعة
- مجتمع ESX للإطار المتميز
- جميع المساهمين في المشروع

---

**CB STORE** - نحو تجربة لعب أفضل 🎮
