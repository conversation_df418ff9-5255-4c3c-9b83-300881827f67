-- CB Pause Menu - Ultra Simple Client (Fixes Blurred Screen)

local isMenuOpen = false
local playerData = {
    playerName = "Player",
    money = 5000,
    playtime = "0 ساعة",
    fps = 60
}

-- Initialize
Citizen.CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Citizen.Wait(100)
    end
    
    playerData.playerName = GetPlayerName(PlayerId())
    print("^2[CB_PAUSEMENU]^7 Ultra simple client loaded!")
end)

-- FPS Counter
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        playerData.fps = math.floor(1.0 / GetFrameTime())
    end
end)

-- Key Handler
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- ESC key
        if IsControlJustPressed(0, 322) then
            ToggleMenu()
        end
        
        -- F1 emergency close
        if IsControlJustPressed(0, 288) then
            if isMenuOpen then
                ForceCloseMenu()
            end
        end
    end
end)

-- Toggle Menu
function ToggleMenu()
    if isMenuOpen then
        CloseMenu()
    else
        OpenMenu()
    end
end

-- Open Menu
function OpenMenu()
    if isMenuOpen then return end
    
    print("^2[CB_PAUSEMENU]^7 Opening menu...")
    isMenuOpen = true
    
    -- Send data to NUI
    SendNUIMessage({
        type = 'openMenu',
        data = playerData
    })
    
    -- Set focus with delay
    Citizen.Wait(100)
    SetNuiFocus(true, true)
end

-- Close Menu
function CloseMenu()
    if not isMenuOpen then return end
    
    print("^2[CB_PAUSEMENU]^7 Closing menu...")
    isMenuOpen = false
    
    -- Clear focus immediately
    SetNuiFocus(false, false)
    
    -- Send close message
    SendNUIMessage({
        type = 'closeMenu'
    })
    
    -- Double check
    Citizen.Wait(50)
    SetNuiFocus(false, false)
end

-- Force Close
function ForceCloseMenu()
    print("^1[CB_PAUSEMENU]^7 Force closing menu...")
    isMenuOpen = false
    
    -- Multiple attempts
    for i = 1, 5 do
        SetNuiFocus(false, false)
        Citizen.Wait(50)
    end
    
    SendNUIMessage({type = 'closeMenu'})
    TriggerEvent('chatMessage', 'SYSTEM', {255, 255, 0}, 'تم إغلاق القائمة')
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(_, cb)
    CloseMenu()
    cb('ok')
end)

RegisterNUICallback('disconnect', function(_, cb)
    DropPlayer(PlayerId(), 'تم قطع الاتصال')
    cb('ok')
end)

-- Commands
RegisterCommand('pausemenu', ToggleMenu, false)
RegisterCommand('fixmouse', ForceCloseMenu, false)
RegisterCommand('forceclosemenu', ForceCloseMenu, false)

RegisterCommand('addmoney', function(_, args)
    local amount = tonumber(args[1]) or 1000
    playerData.money = playerData.money + amount
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم إضافة $' .. amount)
end, false)

-- NUI Monitor (prevents stuck focus)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(2000)
        
        if not isMenuOpen then
            local hasFocus = GetNuiFocus()
            if hasFocus then
                print("^3[CB_PAUSEMENU]^7 Clearing stuck NUI focus...")
                SetNuiFocus(false, false)
            end
        end
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        SetNuiFocus(false, false)
        SendNUIMessage({type = 'closeMenu'})
    end
end)

print("^2[CB_PAUSEMENU]^7 Ultra simple client ready!")
