# CB Pause Menu - استكشاف الأخطاء وإصلاحها

## 🔧 الأخطاء الشائعة وحلولها

### 1. القائمة لا تفتح عند الضغط على ESC

#### الأسباب المحتملة:
- المورد لم يتم تحميله بشكل صحيح
- تضارب مع موارد أخرى تستخدم ESC
- أخطاء في الكود

#### الحلول:
```lua
# في F8 console:
/refresh
/ensure cb_pausemenu

# تحقق من حالة المورد:
/status

# تحقق من الأخطاء:
# راجع server console للأخطاء
```

### 2. خطأ "Config is not defined"

#### السبب:
ملف config.lua لم يتم تحميله أو يحتوي على أخطاء

#### الحل:
```lua
# تأكد من وجود config.lua في المجلد الرئيسي
# تحقق من fxmanifest.lua:
shared_scripts {
    'config.lua'  -- يجب أن يكون موجود
}
```

### 3. خطأ "attempt to call a nil value"

#### السبب:
دالة غير موجودة أو لم يتم تعريفها

#### الحل:
```lua
# استخدم الملفات المبسطة:
- client_simple.lua
- server_simple.lua
- config_simple.lua
- fxmanifest_simple.lua
```

### 4. البيانات لا تظهر في القائمة

#### الأسباب:
- لم يتم تحميل بيانات اللاعب
- أخطاء في الاتصال بين العميل والخادم

#### الحلول:
```lua
# اختبر الأوامر:
/addplaytime 10
/addmoney 5000

# تحقق من console للأخطاء
# أعد تشغيل المورد:
/restart cb_pausemenu
```

### 5. خطأ "Failed to load script"

#### السبب:
أخطاء في بناء الكود (syntax errors)

#### الحل:
```lua
# تحقق من:
- الأقواس المفقودة
- الفواصل المفقودة
- أسماء المتغيرات الخاطئة

# استخدم محرر نصوص يدعم Lua syntax highlighting
```

### 6. مشاكل الصلاحيات (Admin)

#### السبب:
Steam ID غير صحيح في config.lua

#### الحل:
```lua
# احصل على Steam ID الصحيح:
# في F8 console:
print(GetPlayerIdentifier(PlayerId(), 0))

# أضفه في config.lua:
Config.Admins = {
    "steam:YOUR_ACTUAL_STEAM_ID"
}
```

### 7. خطأ "Resource not found"

#### السبب:
اسم المجلد أو المورد غير صحيح

#### الحل:
```lua
# تأكد من:
- اسم المجلد: cb_pausemenu
- في server.cfg: ensure cb_pausemenu
- وجود fxmanifest.lua في المجلد
```

### 8. مشاكل NUI (الواجهة لا تظهر)

#### الأسباب:
- ملفات HTML مفقودة
- أخطاء في JavaScript
- مشاكل في CSS

#### الحلول:
```lua
# تحقق من وجود:
- html/index.html
- html/style.css  
- html/script.js

# في fxmanifest.lua:
files {
    'html/index.html',
    'html/style.css',
    'html/script.js'
}
```

### 9. خطأ "MySQL connection failed"

#### السبب:
محاولة الاتصال بقاعدة البيانات في النسخة المبسطة

#### الحل:
```lua
# استخدم النسخة المبسطة بدون قاعدة بيانات:
- server_simple.lua (بدلاً من server/main.lua)
- لا تحتاج mysql-async في النسخة المبسطة
```

### 10. مشاكل الترميز (Arabic text)

#### السبب:
ترميز الملفات غير صحيح

#### الحل:
```lua
# احفظ الملفات بترميز UTF-8
# في محرر النصوص، اختر:
- Encoding: UTF-8
- Line endings: LF (Unix)
```

## 🛠️ أدوات التشخيص

### 1. أوامر الاختبار:
```lua
/pausemenu          # فتح القائمة
/addplaytime 10     # إضافة وقت لعب
/addmoney 5000      # إضافة مال
/cbadmin            # تحقق من الصلاحيات
```

### 2. فحص Console:
```lua
# في server console:
# ابحث عن رسائل خطأ تبدأ بـ:
- [ERROR]
- [cb_pausemenu]
- script error
```

### 3. فحص الملفات:
```lua
# تأكد من وجود:
✓ fxmanifest.lua
✓ config.lua
✓ client.lua (أو client_simple.lua)
✓ server.lua (أو server_simple.lua)
✓ html/index.html
✓ html/style.css
✓ html/script.js
```

## 🚀 الإعداد السريع (إذا فشل كل شيء)

### 1. احذف كل شيء وابدأ من جديد:
```bash
# احذف مجلد cb_pausemenu
# أعد تحميل الملفات
# استخدم الملفات المبسطة فقط
```

### 2. الحد الأدنى للملفات:
```
cb_pausemenu/
├── fxmanifest_simple.lua → fxmanifest.lua
├── config_simple.lua → config.lua  
├── client_simple.lua → client.lua
├── server_simple.lua → server.lua
└── html/ (جميع الملفات)
```

### 3. server.cfg الأساسي:
```lua
ensure cb_pausemenu
```

### 4. اختبار أساسي:
```lua
# ادخل اللعبة
# اضغط ESC
# يجب أن تفتح القائمة
```

## 📞 طلب المساعدة

عند طلب المساعدة، أرفق:

1. **رسالة الخطأ الكاملة** من console
2. **محتوى fxmanifest.lua**
3. **محتوى config.lua** 
4. **إصدار FiveM** المستخدم
5. **الموارد الأخرى** المثبتة
6. **خطوات إعادة إنتاج المشكلة**

---

**CB STORE** - نحن هنا لمساعدتك! 🛠️
