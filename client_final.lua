-- CB Pause Menu - Final Client Script (100% Working)

local isMenuOpen = false
local playerData = {
    name = "Player",
    id = 1,
    money = 5000,
    bank = 25000,
    job = {name = "unemployed", label = "عاطل"},
    playtime = 0
}
local currentFPS = 60

-- Initialize
Citizen.CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Citizen.Wait(100)
    end

    Citizen.Wait(2000)
    playerData.name = GetPlayerName(PlayerId())
    playerData.id = GetPlayerServerId(PlayerId())
    print("^2[CB_PAUSEMENU]^7 Ready!")
end)

-- FPS Monitor
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        currentFPS = math.floor(1.0 / GetFrameTime())
    end
end)

-- Key Handler
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if IsControlJustPressed(0, Config.MenuKey) then
            ToggleMenu()
        end
        
        -- Emergency close with F1
        if IsControlJustPressed(0, 288) then -- F1
            if isMenuOpen then
                ForceCloseMenu()
            end
        end
    end
end)

-- Toggle Menu Function
function ToggleMenu()
    if isMenuOpen then
        CloseMenu()
    else
        OpenMenu()
    end
end

-- Open Menu Function
function OpenMenu()
    if isMenuOpen then return end
    
    print("^2[CB_PAUSEMENU]^7 Opening menu...")
    
    isMenuOpen = true
    
    -- Get current data
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local rank = GetPlayerRank(playerData.playtime)
    
    -- Send data to NUI
    SendNUIMessage({
        type = 'openMenu',
        data = {
            playerName = playerData.name,
            playerId = playerData.id,
            playtime = FormatPlaytime(playerData.playtime),
            rank = rank,
            money = playerData.money,
            bank = playerData.bank,
            job = playerData.job,
            fps = currentFPS,
            coords = {
                x = math.floor(coords.x),
                y = math.floor(coords.y),
                z = math.floor(coords.z)
            },
            serverName = Config.ServerName,
            maxPlayers = Config.MaxPlayers,
            currentPlayers = #GetActivePlayers()
        }
    })
    
    -- Set NUI focus with delay
    Citizen.Wait(100)
    SetNuiFocus(true, true)
    
    print("^2[CB_PAUSEMENU]^7 Menu opened successfully")
end

-- Close Menu Function
function CloseMenu()
    if not isMenuOpen then return end
    
    print("^2[CB_PAUSEMENU]^7 Closing menu...")
    
    isMenuOpen = false
    
    -- Clear NUI focus immediately
    SetNuiFocus(false, false)
    
    -- Send close message
    SendNUIMessage({
        type = 'closeMenu'
    })
    
    -- Double check focus is cleared
    Citizen.Wait(50)
    SetNuiFocus(false, false)
    
    print("^2[CB_PAUSEMENU]^7 Menu closed successfully")
end

-- Force Close Menu Function
function ForceCloseMenu()
    print("^1[CB_PAUSEMENU]^7 Force closing menu...")
    
    isMenuOpen = false
    
    -- Multiple attempts to clear focus
    for i = 1, 5 do
        SetNuiFocus(false, false)
        Citizen.Wait(50)
    end
    
    SendNUIMessage({type = 'closeMenu'})
    
    TriggerEvent('chatMessage', 'SYSTEM', {255, 255, 0}, 'تم إغلاق القائمة بالقوة')
end

-- Get Player Rank
function GetPlayerRank(playtimeHours)
    local rank = Config.Ranks[1]
    
    for i = #Config.Ranks, 1, -1 do
        if playtimeHours >= Config.Ranks[i].minPlaytime then
            rank = Config.Ranks[i]
            break
        end
    end
    
    return rank
end

-- Format Playtime
function FormatPlaytime(hours)
    if hours < 1 then
        return math.floor(hours * 60) .. " دقيقة"
    elseif hours < 24 then
        return math.floor(hours) .. " ساعة"
    else
        local days = math.floor(hours / 24)
        local remainingHours = math.floor(hours % 24)
        return days .. " يوم " .. remainingHours .. " ساعة"
    end
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(_, cb)
    CloseMenu()
    cb('ok')
end)

RegisterNUICallback('disconnect', function(_, cb)
    TriggerServerEvent('cb_pausemenu:disconnect')
    cb('ok')
end)

RegisterNUICallback('teleportToWaypoint', function(_, cb)
    TeleportToWaypoint()
    cb('ok')
end)

RegisterNUICallback('takeScreenshot', function(_, cb)
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم أخذ لقطة الشاشة')
    cb('ok')
end)

-- Teleport to Waypoint
function TeleportToWaypoint()
    local waypoint = GetFirstBlipInfoId(8)
    if DoesBlipExist(waypoint) then
        local coords = GetBlipInfoIdCoord(waypoint)
        local ground, z = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z, false)
        
        if ground then
            coords.z = z
        end
        
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)
        TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم النقل إلى النقطة المحددة')
    else
        TriggerEvent('chatMessage', 'SYSTEM', {255, 0, 0}, 'لم يتم تحديد نقطة على الخريطة')
    end
end

-- Update Player Data Event
RegisterNetEvent('cb_pausemenu:updatePlayerData')
AddEventHandler('cb_pausemenu:updatePlayerData', function(data)
    if data then
        playerData = data
    end
end)

-- Commands
RegisterCommand('pausemenu', function()
    ToggleMenu()
end, false)

RegisterCommand('closemenu', function()
    if isMenuOpen then
        CloseMenu()
    end
end, false)

RegisterCommand('forceclosemenu', function()
    ForceCloseMenu()
end, false)

RegisterCommand('fixmouse', function()
    ForceCloseMenu()
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم إصلاح مشكلة الماوس')
end, false)

-- Test Commands
RegisterCommand('addplaytime', function(_, args)
    local hours = tonumber(args[1]) or 1
    playerData.playtime = playerData.playtime + hours
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم إضافة ' .. hours .. ' ساعة لوقت اللعب')
end, false)

RegisterCommand('addmoney', function(_, args)
    local amount = tonumber(args[1]) or 1000
    playerData.money = playerData.money + amount
    TriggerEvent('chatMessage', 'SYSTEM', {0, 255, 0}, 'تم إضافة $' .. amount .. ' للمال')
end, false)

-- NUI Focus Monitor
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(2000)
        
        if not isMenuOpen then
            local hasFocus = GetNuiFocus()
            if hasFocus then
                print("^3[CB_PAUSEMENU]^7 Clearing stuck NUI focus...")
                SetNuiFocus(false, false)
            end
        end
    end
end)

-- Resource Stop Handler
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        SetNuiFocus(false, false)
        SendNUIMessage({type = 'closeMenu'})
    end
end)

print("^2[CB_PAUSEMENU]^7 Final client script loaded successfully!")
