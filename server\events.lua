-- Server Events for CB Pause Menu

-- Event: Player requests server statistics
RegisterServerEvent('cb_pausemenu:getServerStats')
AddEventHandler('cb_pausemenu:getServerStats', function()
    local source = source
    local stats = GetServerStatistics()
    
    TriggerClientEvent('cb_pausemenu:updateServerPerformance', source, stats)
end)

-- Event: Player requests online players list
RegisterServerEvent('cb_pausemenu:getOnlinePlayers')
AddEventHandler('cb_pausemenu:getOnlinePlayers', function()
    local source = source
    local players = GetOnlinePlayers()
    
    TriggerClientEvent('cb_pausemenu:updatePlayerList', source, players)
end)

-- Event: Player requests Discord info
RegisterServerEvent('cb_pausemenu:getDiscordInfo')
AddEventHandler('cb_pausemenu:getDiscordInfo', function()
    local source = source
    local discordInfo = GetPlayerDiscordInfo(source)
    
    TriggerClientEvent('cb_pausemenu:updateDiscordAvatar', source, discordInfo.avatar)
end)

-- Event: Player takes screenshot
RegisterServerEvent('cb_pausemenu:takeScreenshot')
AddEventHandler('cb_pausemenu:takeScreenshot', function()
    local source = source
    
    -- Log screenshot action
    LogPlayerAction(source, 'screenshot_taken', {})
    
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم أخذ لقطة الشاشة', 'success')
end)

-- Event: Player reports bug
RegisterServerEvent('cb_pausemenu:reportBug')
AddEventHandler('cb_pausemenu:reportBug', function(description, category)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    local playerName = GetPlayerName(source)
    
    local bugReport = {
        identifier = identifier,
        playerName = playerName,
        description = description,
        category = category,
        timestamp = os.time()
    }
    
    -- Save bug report to database
    if Config.Framework == 'esx' then
        MySQL.Async.execute('INSERT INTO cb_pausemenu_bug_reports (identifier, player_name, description, category, timestamp) VALUES (@identifier, @player_name, @description, @category, @timestamp)', {
            ['@identifier'] = identifier,
            ['@player_name'] = playerName,
            ['@description'] = description,
            ['@category'] = category,
            ['@timestamp'] = os.time()
        })
    end
    
    -- Log bug report
    LogPlayerAction(source, 'bug_reported', bugReport)
    
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم إرسال تقرير الخطأ بنجاح', 'success')
end)

-- Event: Player requests help
RegisterServerEvent('cb_pausemenu:requestHelp')
AddEventHandler('cb_pausemenu:requestHelp', function(message)
    local source = source
    local playerName = GetPlayerName(source)
    
    -- Notify all online admins
    for _, playerId in ipairs(GetPlayers()) do
        if IsPlayerAdmin(playerId) then
            TriggerClientEvent('cb_pausemenu:notify', playerId, 'طلب مساعدة من ' .. playerName .. ': ' .. message, 'info')
        end
    end
    
    -- Log help request
    LogPlayerAction(source, 'help_requested', {message = message})
    
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم إرسال طلب المساعدة للإدارة', 'success')
end)

-- Event: Admin bans player
RegisterServerEvent('cb_pausemenu:banPlayer')
AddEventHandler('cb_pausemenu:banPlayer', function(targetId, reason, duration)
    local source = source
    BanPlayer(source, targetId, reason, duration)
end)

-- Event: Admin kicks player
RegisterServerEvent('cb_pausemenu:kickPlayer')
AddEventHandler('cb_pausemenu:kickPlayer', function(targetId, reason)
    local source = source
    KickPlayer(source, targetId, reason)
end)

-- Event: Admin sends announcement
RegisterServerEvent('cb_pausemenu:sendAnnouncement')
AddEventHandler('cb_pausemenu:sendAnnouncement', function(title, message, type)
    local source = source
    
    if IsPlayerAdmin(source) then
        SendServerAnnouncement(title, message, type)
        TriggerClientEvent('cb_pausemenu:notify', source, 'تم إرسال الإعلان', 'success')
    else
        TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
    end
end)

-- Event: Admin toggles maintenance mode
RegisterServerEvent('cb_pausemenu:toggleMaintenance')
AddEventHandler('cb_pausemenu:toggleMaintenance', function(enabled, message)
    local source = source
    
    if IsPlayerAdmin(source) then
        ToggleMaintenanceMode(enabled, message)
        TriggerClientEvent('cb_pausemenu:notify', source, 'تم تغيير وضع الصيانة', 'success')
    else
        TriggerClientEvent('cb_pausemenu:notify', source, 'ليس لديك صلاحية', 'error')
    end
end)

-- Event: Player changes settings
RegisterServerEvent('cb_pausemenu:updateSettings')
AddEventHandler('cb_pausemenu:updateSettings', function(settings)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if not playerData[identifier] then
        playerData[identifier] = {}
    end
    
    playerData[identifier].settings = settings
    
    -- Save to database
    SavePlayerData(identifier, playerData[identifier])
    
    -- Log settings change
    LogPlayerAction(source, 'settings_updated', settings)
    
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم حفظ الإعدادات', 'success')
end)

-- Event: Player requests achievements
RegisterServerEvent('cb_pausemenu:getAchievements')
AddEventHandler('cb_pausemenu:getAchievements', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    local achievements = {}
    if playerData[identifier] and playerData[identifier].achievements then
        achievements = playerData[identifier].achievements
    end
    
    TriggerClientEvent('cb_pausemenu:updateAchievements', source, achievements)
end)

-- Event: Player unlocks achievement
RegisterServerEvent('cb_pausemenu:unlockAchievement')
AddEventHandler('cb_pausemenu:unlockAchievement', function(achievementId)
    local source = source
    UpdatePlayerAchievement(source, achievementId)
end)

-- Event: Player requests VIP status
RegisterServerEvent('cb_pausemenu:getVIPStatus')
AddEventHandler('cb_pausemenu:getVIPStatus', function()
    local source = source
    local vipStatus = GetPlayerVIPStatus(source)
    
    TriggerClientEvent('cb_pausemenu:updateVIPStatus', source, vipStatus.isVIP, vipStatus.level, vipStatus.expiry)
end)

-- Event: Admin gives VIP to player
RegisterServerEvent('cb_pausemenu:giveVIP')
AddEventHandler('cb_pausemenu:giveVIP', function(targetId, level, duration)
    local source = source
    GivePlayerVIP(source, targetId, level, duration)
end)

-- Event: Player requests friend list
RegisterServerEvent('cb_pausemenu:getFriends')
AddEventHandler('cb_pausemenu:getFriends', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    -- This would need to be implemented based on your friends system
    local friends = {}
    
    TriggerClientEvent('cb_pausemenu:updateOnlineFriends', source, friends)
end)

-- Event: Player accepts friend request
RegisterServerEvent('cb_pausemenu:acceptFriendRequest')
AddEventHandler('cb_pausemenu:acceptFriendRequest', function(requestId)
    local source = source
    
    -- Friend request logic would be implemented here
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم قبول طلب الصداقة', 'success')
end)

-- Event: Player declines friend request
RegisterServerEvent('cb_pausemenu:declineFriendRequest')
AddEventHandler('cb_pausemenu:declineFriendRequest', function(requestId)
    local source = source
    
    -- Friend request logic would be implemented here
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم رفض طلب الصداقة', 'info')
end)

-- Event: Player removes friend
RegisterServerEvent('cb_pausemenu:removeFriend')
AddEventHandler('cb_pausemenu:removeFriend', function(friendId)
    local source = source
    
    -- Remove friend logic would be implemented here
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم حذف الصديق', 'info')
end)

-- Event: Player reports another player
RegisterServerEvent('cb_pausemenu:reportPlayer')
AddEventHandler('cb_pausemenu:reportPlayer', function(targetId, reason, description)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    local playerName = GetPlayerName(source)
    local targetName = GetPlayerName(targetId)
    
    local report = {
        reporterId = identifier,
        reporterName = playerName,
        targetId = targetId,
        targetName = targetName,
        reason = reason,
        description = description,
        timestamp = os.time()
    }
    
    -- Save report to database
    if Config.Framework == 'esx' then
        MySQL.Async.execute('INSERT INTO cb_pausemenu_reports (reporter_id, reporter_name, target_id, target_name, reason, description, timestamp) VALUES (@reporter_id, @reporter_name, @target_id, @target_name, @reason, @description, @timestamp)', {
            ['@reporter_id'] = identifier,
            ['@reporter_name'] = playerName,
            ['@target_id'] = targetId,
            ['@target_name'] = targetName,
            ['@reason'] = reason,
            ['@description'] = description,
            ['@timestamp'] = os.time()
        })
    end
    
    -- Notify admins
    for _, playerId in ipairs(GetPlayers()) do
        if IsPlayerAdmin(playerId) then
            TriggerClientEvent('cb_pausemenu:notify', playerId, 'تقرير جديد من ' .. playerName .. ' ضد ' .. targetName, 'warning')
        end
    end
    
    -- Log report
    LogPlayerAction(source, 'player_reported', report)
    
    TriggerClientEvent('cb_pausemenu:notify', source, 'تم إرسال التقرير للإدارة', 'success')
end)
