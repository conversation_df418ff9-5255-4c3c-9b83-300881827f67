-- Client Functions for CB Pause Menu

-- Function to get player statistics
function GetPlayerStats()
    local playerPed = PlayerPedId()
    local health = GetEntityHealth(playerPed)
    local armor = GetPedArmour(playerPed)
    local stamina = GetPlayerStamina(PlayerId())
    
    return {
        health = health,
        armor = armor,
        stamina = stamina,
        wanted = GetPlayerWantedLevel(PlayerId())
    }
end

-- Function to get vehicle information
function GetVehicleInfo()
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    
    if vehicle ~= 0 then
        local model = GetEntityModel(vehicle)
        local displayName = GetDisplayNameFromVehicleModel(model)
        local plate = GetVehicleNumberPlateText(vehicle)
        local fuel = GetVehicleFuelLevel(vehicle)
        local engine = GetVehicleEngineHealth(vehicle)
        local body = GetVehicleBodyHealth(vehicle)
        
        return {
            inVehicle = true,
            model = displayName,
            plate = plate,
            fuel = math.floor(fuel),
            engine = math.floor(engine / 10),
            body = math.floor(body / 10),
            speed = math.floor(GetEntitySpeed(vehicle) * 3.6) -- Convert to km/h
        }
    else
        return {
            inVehicle = false
        }
    end
end

-- Function to get location information
function GetLocationInfo()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local streetHash, crossingHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local streetName = GetStreetNameFromHashKey(streetHash)
    local crossingName = GetStreetNameFromHashKey(crossingHash)
    local zone = GetNameOfZone(coords.x, coords.y, coords.z)
    
    return {
        street = streetName,
        crossing = crossingName,
        zone = zone,
        coords = {
            x = math.floor(coords.x),
            y = math.floor(coords.y),
            z = math.floor(coords.z)
        }
    }
end

-- Function to get weather and time
function GetEnvironmentInfo()
    local weather = GetPrevWeatherTypeHashName()
    local time = GetClockHours() .. ":" .. string.format("%02d", GetClockMinutes())
    
    return {
        weather = weather,
        time = time,
        day = GetClockDayOfMonth(),
        month = GetClockMonth(),
        year = GetClockYear()
    }
end

-- Function to toggle HUD elements
function ToggleHudElement(element, state)
    if element == 'minimap' then
        DisplayRadar(state)
    elseif element == 'hud' then
        DisplayHud(state)
    elseif element == 'chat' then
        -- This would need to be implemented based on your chat resource
        TriggerEvent('chat:toggle', state)
    end
end

-- Function to change graphics settings
function ChangeGraphicsSettings(setting, value)
    if setting == 'fov' then
        SetCamFov(GetRenderingCam(), value)
    elseif setting == 'brightness' then
        -- This would need custom implementation
        SetArtificialLightsState(value > 50)
    end
end

-- Function to take screenshot
function TakeScreenshot()
    exports['screenshot-basic']:requestScreenshotUpload('https://your-webhook-url.com', 'files[]', function(data)
        local resp = json.decode(data)
        if resp and resp.attachments and resp.attachments[1] and resp.attachments[1].url then
            ESX.ShowNotification('تم حفظ لقطة الشاشة')
        else
            ESX.ShowNotification('فشل في حفظ لقطة الشاشة')
        end
    end)
end

-- Function to get server performance
function GetServerPerformance()
    return {
        fps = currentFPS,
        ping = GetPlayerPing(PlayerId()),
        players = #GetActivePlayers(),
        maxPlayers = Config.MaxPlayers
    }
end

-- Function to format number with commas
function FormatNumber(num)
    local formatted = tostring(num)
    local k
    while true do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
        if k == 0 then
            break
        end
    end
    return formatted
end

-- Function to get player's online friends
function GetOnlineFriends()
    -- This would need to be implemented with your friends system
    return {}
end

-- Function to send friend request
function SendFriendRequest(playerId)
    TriggerServerEvent('cb_pausemenu:sendFriendRequest', playerId)
end

-- Function to get player inventory
function GetPlayerInventory()
    if Config.Framework == 'vrp' then
        -- Request inventory from server
        local inventory = {}
        TriggerServerEvent('cb_pausemenu:getInventory')
        return inventory
    end
    return {}
end

-- Function to get player weapons
function GetPlayerWeapons()
    local weapons = {}
    local playerPed = PlayerPedId()
    
    for _, weapon in pairs(Config.Weapons or {}) do
        if HasPedGotWeapon(playerPed, GetHashKey(weapon.name), false) then
            local ammo = GetAmmoInPedWeapon(playerPed, GetHashKey(weapon.name))
            table.insert(weapons, {
                name = weapon.name,
                label = weapon.label,
                ammo = ammo
            })
        end
    end
    
    return weapons
end

-- Function to play sound effect
function PlaySoundEffect(soundName)
    PlaySoundFrontend(-1, soundName, "HUD_FRONTEND_DEFAULT_SOUNDSET", true)
end

-- Function to create notification
function CreateNotification(message, type, duration)
    -- Use chat message as fallback
    TriggerEvent('chatMessage', 'SYSTEM', {255, 255, 0}, message)
end

-- Function to get player achievements
function GetPlayerAchievements()
    -- This would need to be implemented with your achievements system
    return {}
end

-- Function to toggle god mode (admin only)
function ToggleGodMode()
    TriggerServerEvent('cb_pausemenu:toggleGodMode')
end

-- Function to toggle invisible mode (admin only)
function ToggleInvisible()
    TriggerServerEvent('cb_pausemenu:toggleInvisible')
end

-- Function to teleport to player (admin only)
function TeleportToPlayer(playerId)
    TriggerServerEvent('cb_pausemenu:teleportToPlayer', playerId)
end
