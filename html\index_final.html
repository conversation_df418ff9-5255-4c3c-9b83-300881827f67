<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CB Pause Menu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: transparent;
            color: white;
            overflow: hidden;
            user-select: none;
        }

        .menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: none;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
        }

        .menu.show {
            display: flex;
        }

        .container {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            border-radius: 20px;
            padding: 30px;
            max-width: 900px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(0, 212, 255, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #00d4ff;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #00d4ff;
            font-size: 32px;
            margin-bottom: 10px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 0, 0, 0.8);
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 0, 0, 1);
            transform: scale(1.1);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .info-card h3 {
            color: #00d4ff;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .label {
            color: #ccc;
            font-size: 14px;
        }

        .value {
            color: #fff;
            font-weight: bold;
            font-size: 14px;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            text-transform: uppercase;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 212, 255, 0.4);
        }

        .btn.danger {
            background: linear-gradient(45deg, #ff4757, #ff3742);
        }

        .btn.danger:hover {
            box-shadow: 0 8px 20px rgba(255, 71, 87, 0.4);
        }

        .fps {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: bold;
            color: #00d4ff;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #00d4ff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="fps" id="fps">FPS: 60</div>

    <div class="menu" id="menu">
        <div class="container">
            <button class="close-btn" onclick="closeMenu()">&times;</button>
            
            <div class="header">
                <h1>CB STORE SERVER</h1>
                <p>قائمة الإيقاف المؤقت</p>
            </div>

            <div class="info-grid">
                <div class="info-card">
                    <h3>معلومات اللاعب</h3>
                    <div class="info-row">
                        <span class="label">اسم اللاعب:</span>
                        <span class="value" id="playerName">Player</span>
                    </div>
                    <div class="info-row">
                        <span class="label">معرف اللاعب:</span>
                        <span class="value" id="playerId">1</span>
                    </div>
                    <div class="info-row">
                        <span class="label">وقت اللعب:</span>
                        <span class="value" id="playtime">0 ساعة</span>
                    </div>
                    <div class="info-row">
                        <span class="label">الرتبة:</span>
                        <span class="value" id="rank">مبتدئ</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>المعلومات المالية</h3>
                    <div class="info-row">
                        <span class="label">المال النقدي:</span>
                        <span class="value" id="money">$5000</span>
                    </div>
                    <div class="info-row">
                        <span class="label">المال البنكي:</span>
                        <span class="value" id="bank">$25000</span>
                    </div>
                    <div class="info-row">
                        <span class="label">الوظيفة:</span>
                        <span class="value" id="job">عاطل</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>معلومات الخادم</h3>
                    <div class="info-row">
                        <span class="label">معدل الإطارات:</span>
                        <span class="value" id="fpsValue">60 FPS</span>
                    </div>
                    <div class="info-row">
                        <span class="label">اللاعبين المتصلين:</span>
                        <span class="value" id="players">1/64</span>
                    </div>
                    <div class="info-row">
                        <span class="label">الموقع:</span>
                        <span class="value" id="coords">0, 0, 0</span>
                    </div>
                </div>
            </div>

            <div class="actions">
                <button class="btn" onclick="takeScreenshot()">لقطة شاشة</button>
                <button class="btn" onclick="teleportToWaypoint()">النقل للنقطة</button>
                <button class="btn danger" onclick="disconnect()">قطع الاتصال</button>
                <button class="btn" onclick="closeMenu()">إغلاق</button>
            </div>
        </div>
    </div>

    <script>
        let isMenuOpen = false;

        window.addEventListener('message', function(event) {
            const data = event.data;
            
            if (data.type === 'openMenu') {
                openMenu(data.data);
            } else if (data.type === 'closeMenu') {
                closeMenu();
            } else if (data.type === 'updateFPS') {
                updateFPS(data.fps);
            }
        });

        function openMenu(data) {
            isMenuOpen = true;
            document.getElementById('menu').classList.add('show');
            
            if (data) {
                document.getElementById('playerName').textContent = data.playerName || 'Player';
                document.getElementById('playerId').textContent = data.playerId || '1';
                document.getElementById('playtime').textContent = data.playtime || '0 ساعة';
                document.getElementById('rank').textContent = data.rank?.name || 'مبتدئ';
                document.getElementById('money').textContent = '$' + (data.money || 5000);
                document.getElementById('bank').textContent = '$' + (data.bank || 25000);
                document.getElementById('job').textContent = data.job?.label || 'عاطل';
                document.getElementById('fpsValue').textContent = (data.fps || 60) + ' FPS';
                document.getElementById('players').textContent = (data.currentPlayers || 1) + '/' + (data.maxPlayers || 64);
                
                if (data.coords) {
                    document.getElementById('coords').textContent = data.coords.x + ', ' + data.coords.y + ', ' + data.coords.z;
                }
            }
        }

        function closeMenu() {
            isMenuOpen = false;
            document.getElementById('menu').classList.remove('show');
            
            fetch(`https://${getResourceName()}/closeMenu`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            }).catch(() => {});
        }

        function updateFPS(fps) {
            document.getElementById('fps').textContent = 'FPS: ' + fps;
            document.getElementById('fpsValue').textContent = fps + ' FPS';
        }

        function takeScreenshot() {
            fetch(`https://${getResourceName()}/takeScreenshot`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            }).catch(() => {});
        }

        function teleportToWaypoint() {
            fetch(`https://${getResourceName()}/teleportToWaypoint`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            }).catch(() => {});
        }

        function disconnect() {
            if (confirm('هل أنت متأكد من قطع الاتصال؟')) {
                fetch(`https://${getResourceName()}/disconnect`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({})
                }).catch(() => {});
            }
        }

        function getResourceName() {
            return window.location.hostname || 'cb_pausemenu';
        }

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isMenuOpen) {
                closeMenu();
            }
        });

        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        console.log('CB Pause Menu Final HTML loaded');
    </script>
</body>
</html>
