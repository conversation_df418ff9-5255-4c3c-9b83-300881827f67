@echo off
echo.
echo ========================================
echo   CB PAUSE MENU - EMERGENCY FIX
echo ========================================
echo.
echo This will fix the blurred screen issue
echo and make the menu work properly.
echo.
pause

echo.
echo [1/5] Backing up original files...
if exist fxmanifest.lua (
    copy fxmanifest.lua fxmanifest.lua.backup >nul 2>&1
    echo - fxmanifest.lua backed up
)
if exist client.lua (
    copy client.lua client.lua.backup >nul 2>&1
    echo - client.lua backed up
)
if exist html\index.html (
    copy html\index.html html\index.html.backup >nul 2>&1
    echo - index.html backed up
)

echo.
echo [2/5] Applying fixed files...
if exist fxmanifest_simple.lua (
    copy fxmanifest_simple.lua fxmanifest.lua >nul 2>&1
    echo - fxmanifest.lua fixed
) else (
    echo - ERROR: fxmanifest_simple.lua not found!
)

if exist client_simple.lua (
    copy client_simple.lua client.lua >nul 2>&1
    echo - client.lua fixed
) else (
    echo - ERROR: client_simple.lua not found!
)

if exist server_simple.lua (
    copy server_simple.lua server.lua >nul 2>&1
    echo - server.lua fixed
) else (
    echo - ERROR: server_simple.lua not found!
)

if exist config_simple.lua (
    copy config_simple.lua config.lua >nul 2>&1
    echo - config.lua fixed
) else (
    echo - ERROR: config_simple.lua not found!
)

if exist html\index_simple.html (
    copy html\index_simple.html html\index.html >nul 2>&1
    echo - index.html fixed
) else (
    echo - ERROR: html\index_simple.html not found!
)

echo.
echo [3/5] Cleaning up unnecessary files...
if exist html\style.css (
    ren html\style.css style.css.backup >nul 2>&1
    echo - style.css disabled (not needed)
)
if exist html\script.js (
    ren html\script.js script.js.backup >nul 2>&1
    echo - script.js disabled (not needed)
)

echo.
echo [4/5] Verifying installation...
if exist fxmanifest.lua (
    echo - fxmanifest.lua: OK
) else (
    echo - fxmanifest.lua: MISSING!
)

if exist client.lua (
    echo - client.lua: OK
) else (
    echo - client.lua: MISSING!
)

if exist server.lua (
    echo - server.lua: OK
) else (
    echo - server.lua: MISSING!
)

if exist config.lua (
    echo - config.lua: OK
) else (
    echo - config.lua: MISSING!
)

if exist html\index.html (
    echo - html\index.html: OK
) else (
    echo - html\index.html: MISSING!
)

echo.
echo [5/5] Fix completed!
echo.
echo ========================================
echo   EMERGENCY FIX APPLIED SUCCESSFULLY
echo ========================================
echo.
echo NEXT STEPS:
echo 1. Add your Steam ID to config.lua
echo 2. Add 'ensure cb_pausemenu' to server.cfg
echo 3. Restart your server
echo 4. Test with ESC key in game
echo.
echo EMERGENCY COMMANDS (if menu gets stuck):
echo - /forceclosemenu (force close)
echo - /closemenu (normal close)
echo - F1 key (emergency close)
echo.
echo BACKUP FILES CREATED:
echo - fxmanifest.lua.backup
echo - client.lua.backup  
echo - html\index.html.backup
echo.
echo If you need to restore originals, rename
echo the .backup files back to original names.
echo.
pause
