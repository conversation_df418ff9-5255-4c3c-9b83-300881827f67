-- CB Pause Menu - Final Server Script (100% Working)

local playerData = {}
local playerPlaytime = {}

-- Server startup
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('^2[CB_PAUSEMENU]^7 Final server script started!')
        playerData = {}
        playerPlaytime = {}
    end
end)

-- Player connecting
AddEventHandler('playerConnecting', function(name)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if not playerData[identifier] then
        playerData[identifier] = {
            playtime = 0,
            lastSeen = os.time(),
            rank = 1,
            money = 5000,
            bank = 25000,
            job = {name = "unemployed", label = "عاطل"},
            achievements = {},
            settings = {}
        }
    end
    
    playerPlaytime[source] = {
        identifier = identifier,
        joinTime = os.time()
    }
    
    print('^2[CB_PAUSEMENU]^7 Player ' .. name .. ' connected')
end)

-- Player disconnecting
AddEvent<PERSON>andler('playerDropped', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if playerPlaytime[source] then
        local sessionTime = os.time() - playerPlaytime[source].joinTime
        
        if playerData[identifier] then
            playerData[identifier].playtime = playerData[identifier].playtime + (sessionTime / 3600)
            playerData[identifier].lastSeen = os.time()
        end
        
        playerPlaytime[source] = nil
    end
end)

-- Get player data
RegisterServerEvent('cb_pausemenu:getPlayerData')
AddEventHandler('cb_pausemenu:getPlayerData', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if identifier and playerData[identifier] then
        local sessionTime = 0
        if playerPlaytime[source] then
            sessionTime = os.time() - playerPlaytime[source].joinTime
        end
        
        local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
        
        TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
            playtime = totalPlaytime,
            money = playerData[identifier].money,
            bank = playerData[identifier].bank,
            job = playerData[identifier].job,
            playerData = playerData[identifier]
        })
    end
end)

-- Player disconnect
RegisterServerEvent('cb_pausemenu:disconnect')
AddEventHandler('cb_pausemenu:disconnect', function()
    local source = source
    local playerName = GetPlayerName(source)
    
    print('^3[CB_PAUSEMENU]^7 Player ' .. playerName .. ' disconnected via menu')
    DropPlayer(source, 'تم قطع الاتصال')
end)

-- Admin give money
RegisterServerEvent('cb_pausemenu:giveMoney')
AddEventHandler('cb_pausemenu:giveMoney', function(targetId, amount, account)
    local source = source
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    
    if IsPlayerAdmin(source) then
        if targetIdentifier and playerData[targetIdentifier] then
            if account == "bank" then
                playerData[targetIdentifier].bank = (playerData[targetIdentifier].bank or 0) + amount
            else
                playerData[targetIdentifier].money = (playerData[targetIdentifier].money or 0) + amount
            end
            
            TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 0}, 'تم إعطاء $' .. amount)
            TriggerClientEvent('chatMessage', targetId, 'SYSTEM', {0, 255, 0}, 'تم إعطاؤك $' .. amount)
        end
    else
        TriggerClientEvent('chatMessage', source, 'SYSTEM', {255, 0, 0}, 'ليس لديك صلاحية')
    end
end)

-- Admin kick player
RegisterServerEvent('cb_pausemenu:kickPlayer')
AddEventHandler('cb_pausemenu:kickPlayer', function(targetId, reason)
    local source = source
    
    if IsPlayerAdmin(source) then
        local targetName = GetPlayerName(targetId)
        if targetName then
            DropPlayer(targetId, 'تم طردك. السبب: ' .. (reason or 'غير محدد'))
            TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 0}, 'تم طرد: ' .. targetName)
        end
    else
        TriggerClientEvent('chatMessage', source, 'SYSTEM', {255, 0, 0}, 'ليس لديك صلاحية')
    end
end)

-- Get online players
RegisterServerEvent('cb_pausemenu:getOnlinePlayers')
AddEventHandler('cb_pausemenu:getOnlinePlayers', function()
    local source = source
    local players = {}
    
    for _, playerId in ipairs(GetPlayers()) do
        local name = GetPlayerName(playerId)
        if name then
            table.insert(players, {
                id = playerId,
                name = name,
                ping = GetPlayerPing(playerId)
            })
        end
    end
    
    TriggerClientEvent('cb_pausemenu:updatePlayerList', source, players)
end)

-- Check if player is admin
function IsPlayerAdmin(source)
    local identifier = GetPlayerIdentifier(source, 0)
    
    local admins = {
        "steam:110000100000000",
        "license:1234567890abcdef"
    }
    
    for _, admin in pairs(admins) do
        if identifier == admin then
            return true
        end
    end
    
    return false
end

-- Admin commands
RegisterCommand('cbadmin', function(source)
    if IsPlayerAdmin(source) then
        TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 255}, 'أوامر الإدارة متاحة')
    else
        TriggerClientEvent('chatMessage', source, 'SYSTEM', {255, 0, 0}, 'ليس لديك صلاحية')
    end
end, false)

RegisterCommand('setmoney', function(source, args)
    if IsPlayerAdmin(source) then
        local targetId = tonumber(args[1])
        local amount = tonumber(args[2]) or 5000
        
        if targetId and GetPlayerName(targetId) then
            local identifier = GetPlayerIdentifier(targetId, 0)
            if playerData[identifier] then
                playerData[identifier].money = amount
                TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 0}, 'تم تعديل المال إلى $' .. amount)
                TriggerClientEvent('chatMessage', targetId, 'SYSTEM', {0, 255, 0}, 'تم تعديل مالك إلى $' .. amount)
            end
        end
    end
end, true)

-- Periodic playtime update
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(60000) -- Every minute
        
        for source, data in pairs(playerPlaytime) do
            if GetPlayerName(source) then
                local identifier = data.identifier
                local sessionTime = os.time() - data.joinTime
                
                if playerData[identifier] then
                    local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
                    
                    TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
                        playtime = totalPlaytime,
                        money = playerData[identifier].money,
                        bank = playerData[identifier].bank,
                        job = playerData[identifier].job,
                        playerData = playerData[identifier]
                    })
                end
            end
        end
    end
end)

print("^2[CB_PAUSEMENU]^7 Final server script loaded!")
