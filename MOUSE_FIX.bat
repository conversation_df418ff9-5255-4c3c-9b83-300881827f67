@echo off
echo.
echo ========================================
echo   CB PAUSE MENU - MOUSE FIX
echo ========================================
echo.
echo This will fix:
echo - Stuck mouse cursor
echo - Blurred menu screen  
echo - Control issues
echo - NUI focus problems
echo.
pause

echo.
echo [1/4] Backing up current files...
if exist client.lua (
    copy client.lua client.lua.backup >nul 2>&1
    echo - client.lua backed up
)
if exist html\index.html (
    copy html\index.html html\index.html.backup >nul 2>&1
    echo - index.html backed up
)

echo.
echo [2/4] Applying mouse fixes...
if exist client_simple.lua (
    copy client_simple.lua client.lua >nul 2>&1
    echo - client.lua fixed (mouse controls)
) else (
    echo - ERROR: client_simple.lua not found!
)

if exist html\index_simple.html (
    copy html\index_simple.html html\index.html >nul 2>&1
    echo - index.html fixed (NUI focus)
) else (
    echo - ERROR: html\index_simple.html not found!
)

if exist fxmanifest_simple.lua (
    copy fxmanifest_simple.lua fxmanifest.lua >nul 2>&1
    echo - fxmanifest.lua updated
)

echo.
echo [3/4] Verifying fixes...
if exist client.lua (
    echo - client.lua: OK
) else (
    echo - client.lua: MISSING!
)

if exist html\index.html (
    echo - html\index.html: OK
) else (
    echo - html\index.html: MISSING!
)

echo.
echo [4/4] Mouse fix completed!
echo.
echo ========================================
echo   MOUSE FIX APPLIED SUCCESSFULLY
echo ========================================
echo.
echo WHAT'S FIXED:
echo - NUI focus management improved
echo - Mouse cursor auto-cleanup
echo - Emergency close commands added
echo - Blurred screen issue resolved
echo.
echo EMERGENCY COMMANDS (if mouse gets stuck):
echo - /fixmouse (fix mouse cursor)
echo - /forceclosemenu (force close menu)
echo - /closemenu (normal close)
echo - F1 key (emergency close)
echo.
echo TESTING STEPS:
echo 1. Restart your server
echo 2. Press ESC to open menu
echo 3. Click buttons to test interaction
echo 4. Press ESC to close menu
echo 5. Check that mouse cursor disappears
echo 6. Test game controls work normally
echo.
echo IF MOUSE STILL STUCK:
echo 1. Press F1 key
echo 2. Use /fixmouse command
echo 3. Use /forceclosemenu command
echo 4. Restart the resource
echo.
echo BACKUP FILES CREATED:
echo - client.lua.backup
echo - html\index.html.backup
echo.
pause
