-- Utility Library for CB Pause Menu
-- Common utility functions and helpers

local Utils = {}

-- String utilities
Utils.string = {}

-- Split string by delimiter
function Utils.string.split(str, delimiter)
    local result = {}
    local pattern = "(.-)" .. delimiter
    local lastEnd = 1
    local s, e, cap = str:find(pattern, 1)
    
    while s do
        if s ~= 1 or cap ~= "" then
            table.insert(result, cap)
        end
        lastEnd = e + 1
        s, e, cap = str:find(pattern, lastEnd)
    end
    
    if lastEnd <= #str then
        cap = str:sub(lastEnd)
        table.insert(result, cap)
    end
    
    return result
end

-- Trim whitespace from string
function Utils.string.trim(str)
    return str:match("^%s*(.-)%s*$")
end

-- Check if string starts with prefix
function Utils.string.startsWith(str, prefix)
    return str:sub(1, #prefix) == prefix
end

-- Check if string ends with suffix
function Utils.string.endsWith(str, suffix)
    return str:sub(-#suffix) == suffix
end

-- Table utilities
Utils.table = {}

-- Deep copy table
function Utils.table.deepCopy(original)
    local copy = {}
    for k, v in pairs(original) do
        if type(v) == "table" then
            copy[k] = Utils.table.deepCopy(v)
        else
            copy[k] = v
        end
    end
    return copy
end

-- Merge two tables
function Utils.table.merge(t1, t2)
    local result = Utils.table.deepCopy(t1)
    for k, v in pairs(t2) do
        if type(v) == "table" and type(result[k]) == "table" then
            result[k] = Utils.table.merge(result[k], v)
        else
            result[k] = v
        end
    end
    return result
end

-- Check if table contains value
function Utils.table.contains(tbl, value)
    for _, v in pairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end

-- Get table size
function Utils.table.size(tbl)
    local count = 0
    for _ in pairs(tbl) do
        count = count + 1
    end
    return count
end

-- Math utilities
Utils.math = {}

-- Round number to specified decimal places
function Utils.math.round(num, decimals)
    local mult = 10 ^ (decimals or 0)
    return math.floor(num * mult + 0.5) / mult
end

-- Clamp number between min and max
function Utils.math.clamp(num, min, max)
    return math.max(min, math.min(max, num))
end

-- Linear interpolation
function Utils.math.lerp(a, b, t)
    return a + (b - a) * t
end

-- Distance between two points
function Utils.math.distance(x1, y1, z1, x2, y2, z2)
    local dx = x2 - x1
    local dy = y2 - y1
    local dz = (z2 or 0) - (z1 or 0)
    return math.sqrt(dx * dx + dy * dy + dz * dz)
end

-- Time utilities
Utils.time = {}

-- Format seconds to human readable time
function Utils.time.formatTime(seconds)
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    if hours > 0 then
        return string.format("%02d:%02d:%02d", hours, minutes, secs)
    else
        return string.format("%02d:%02d", minutes, secs)
    end
end

-- Get current timestamp
function Utils.time.getTimestamp()
    return os.time()
end

-- Format timestamp to date string
function Utils.time.formatDate(timestamp, format)
    format = format or "%Y-%m-%d %H:%M:%S"
    return os.date(format, timestamp)
end

-- Color utilities
Utils.color = {}

-- Convert hex color to RGB
function Utils.color.hexToRgb(hex)
    hex = hex:gsub("#", "")
    return {
        r = tonumber("0x" .. hex:sub(1, 2)),
        g = tonumber("0x" .. hex:sub(3, 4)),
        b = tonumber("0x" .. hex:sub(5, 6))
    }
end

-- Convert RGB to hex color
function Utils.color.rgbToHex(r, g, b)
    return string.format("#%02x%02x%02x", r, g, b)
end

-- Validation utilities
Utils.validate = {}

-- Check if value is a valid number
function Utils.validate.isNumber(value)
    return type(value) == "number" and not (value ~= value) -- NaN check
end

-- Check if value is a valid string
function Utils.validate.isString(value)
    return type(value) == "string"
end

-- Check if value is a valid table
function Utils.validate.isTable(value)
    return type(value) == "table"
end

-- Check if value is a valid function
function Utils.validate.isFunction(value)
    return type(value) == "function"
end

-- File utilities
Utils.file = {}

-- Check if file exists
function Utils.file.exists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- Read file content
function Utils.file.read(path)
    local file = io.open(path, "r")
    if file then
        local content = file:read("*all")
        file:close()
        return content
    end
    return nil
end

-- Write content to file
function Utils.file.write(path, content)
    local file = io.open(path, "w")
    if file then
        file:write(content)
        file:close()
        return true
    end
    return false
end

-- JSON utilities
Utils.json = {}

-- Safe JSON encode
function Utils.json.encode(data)
    local success, result = pcall(json.encode, data)
    if success then
        return result
    else
        print("^1[ERROR]^7 Failed to encode JSON: " .. tostring(result))
        return nil
    end
end

-- Safe JSON decode
function Utils.json.decode(str)
    local success, result = pcall(json.decode, str)
    if success then
        return result
    else
        print("^1[ERROR]^7 Failed to decode JSON: " .. tostring(result))
        return nil
    end
end

-- Debug utilities
Utils.debug = {}

-- Print table contents
function Utils.debug.printTable(tbl, indent)
    indent = indent or 0
    local spacing = string.rep("  ", indent)
    
    for k, v in pairs(tbl) do
        if type(v) == "table" then
            print(spacing .. tostring(k) .. ":")
            Utils.debug.printTable(v, indent + 1)
        else
            print(spacing .. tostring(k) .. ": " .. tostring(v))
        end
    end
end

-- Benchmark function execution time
function Utils.debug.benchmark(func, ...)
    local startTime = GetGameTimer()
    local result = {func(...)}
    local endTime = GetGameTimer()
    
    print("^3[BENCHMARK]^7 Function executed in " .. (endTime - startTime) .. "ms")
    return table.unpack(result)
end

-- Export utils
_G.Utils = Utils

return Utils
