/* CB Pause Menu Styles */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

/* Background Blur */
.background-blur {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    z-index: 1000;
}

/* Main Menu Container */
.pause-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-container {
    width: 90%;
    max-width: 1200px;
    height: 80%;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Header */
.menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.server-logo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #00d4ff;
}

.server-info h1 {
    color: #fff;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.server-stats {
    color: #00d4ff;
    font-size: 14px;
}

.close-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 0, 0, 0.3);
    transform: scale(1.1);
}

.close-btn i {
    color: #fff;
    font-size: 18px;
}

/* Content Area */
.menu-content {
    display: flex;
    height: calc(100% - 100px);
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Player Card */
.player-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
}

.player-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
}

.player-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid #00d4ff;
}

.player-status {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid #fff;
}

.player-status.online {
    background: #00ff00;
}

.player-info h3 {
    color: #fff;
    font-size: 18px;
    margin-bottom: 10px;
}

.player-rank {
    margin-bottom: 15px;
}

.player-rank span {
    color: #00d4ff;
    font-weight: 600;
}

.rank-progress {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    margin-top: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #0099cc);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.player-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ccc;
    font-size: 14px;
}

.stat i {
    color: #00d4ff;
    width: 16px;
}

/* Navigation Menu */
.nav-menu ul {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    color: #ccc;
    cursor: pointer;
    border-radius: 10px;
    transition: all 0.3s ease;
    margin-bottom: 5px;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.nav-item.active {
    background: linear-gradient(90deg, #00d4ff, #0099cc);
    color: #fff;
}

.nav-item i {
    font-size: 16px;
    width: 20px;
}

/* Quick Actions */
.quick-actions {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 8px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.action-btn.disconnect {
    background: rgba(255, 0, 0, 0.3);
}

.action-btn.disconnect:hover {
    background: rgba(255, 0, 0, 0.5);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-section h2 {
    color: #fff;
    font-size: 28px;
    margin-bottom: 25px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.info-card h3 {
    color: #00d4ff;
    font-size: 18px;
    margin-bottom: 15px;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item .label {
    color: #ccc;
    font-size: 14px;
}

.stat-item .value {
    color: #fff;
    font-weight: 600;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.settings-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
}

.settings-card h3 {
    color: #00d4ff;
    margin-bottom: 20px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.setting-item label {
    color: #fff;
    font-size: 14px;
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
}

.setting-item input[type="range"] {
    width: 120px;
}

/* Performance Grid */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.performance-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
}

.fps-display {
    margin: 20px 0;
}

.fps-value {
    font-size: 48px;
    font-weight: 700;
    color: #00d4ff;
}

.fps-label {
    color: #ccc;
    font-size: 16px;
    margin-left: 10px;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    padding: 15px 20px;
    border-radius: 8px;
    border-left: 4px solid #00d4ff;
    animation: slideInRight 0.3s ease;
    max-width: 300px;
}

.notification.success {
    border-left-color: #00ff00;
}

.notification.error {
    border-left-color: #ff0000;
}

.notification.warning {
    border-left-color: #ffaa00;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-container {
        width: 95%;
        height: 90%;
    }
    
    .menu-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .info-grid,
    .settings-grid,
    .performance-grid {
        grid-template-columns: 1fr;
    }
}
