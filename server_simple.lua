-- CB Pause Menu - Simple Server Script for Testing

local playerData = {}
local playerPlaytime = {}

-- Server startup
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('^2[CB_PAUSEMENU]^7 Simple server script started successfully!')
        
        -- Initialize data storage
        playerData = {}
        playerPlaytime = {}
    end
end)

-- Player connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    -- Initialize player data
    if not playerData[identifier] then
        playerData[identifier] = {
            playtime = 0,
            lastSeen = os.time(),
            rank = 1,
            money = 5000,
            bank = 25000,
            job = {name = "unemployed", label = "عاطل"},
            achievements = {},
            settings = {}
        }
    end
    
    playerPlaytime[source] = {
        identifier = identifier,
        joinTime = os.time()
    }
    
    print('^2[CB_PAUSEMENU]^7 Player ' .. name .. ' connected with ID: ' .. identifier)
end)

-- Player disconnecting
AddEventHandler('playerDropped', function(reason)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if playerPlaytime[source] then
        local sessionTime = os.time() - playerPlaytime[source].joinTime
        
        if playerData[identifier] then
            playerData[identifier].playtime = playerData[identifier].playtime + (sessionTime / 3600) -- Convert to hours
            playerData[identifier].lastSeen = os.time()
            
            print('^2[CB_PAUSEMENU]^7 Saved playtime for player: ' .. identifier .. ' (' .. math.floor(sessionTime/60) .. ' minutes)')
        end
        
        playerPlaytime[source] = nil
    end
end)

-- Event: Get player data
RegisterServerEvent('cb_pausemenu:getPlayerData')
AddEventHandler('cb_pausemenu:getPlayerData', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if identifier and playerData[identifier] then
        local sessionTime = 0
        if playerPlaytime[source] then
            sessionTime = os.time() - playerPlaytime[source].joinTime
        end
        
        local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
        
        TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
            playtime = totalPlaytime,
            money = playerData[identifier].money or 5000,
            bank = playerData[identifier].bank or 25000,
            job = playerData[identifier].job or {name = "unemployed", label = "عاطل"},
            playerData = playerData[identifier]
        })
    end
end)

-- Event: Player disconnect
RegisterServerEvent('cb_pausemenu:disconnect')
AddEventHandler('cb_pausemenu:disconnect', function()
    local source = source
    local playerName = GetPlayerName(source)
    
    print('^3[CB_PAUSEMENU]^7 Player ' .. playerName .. ' disconnected via menu')
    DropPlayer(source, 'تم قطع الاتصال بواسطة اللاعب')
end)

-- Event: Admin give money
RegisterServerEvent('cb_pausemenu:giveMoney')
AddEventHandler('cb_pausemenu:giveMoney', function(targetId, amount, account)
    local source = source
    local adminIdentifier = GetPlayerIdentifier(source, 0)
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    
    -- Check if admin
    if IsPlayerAdmin(source) then
        if targetIdentifier and playerData[targetIdentifier] then
            if account == "bank" then
                playerData[targetIdentifier].bank = (playerData[targetIdentifier].bank or 0) + amount
            else
                playerData[targetIdentifier].money = (playerData[targetIdentifier].money or 0) + amount
            end
            
            TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 0}, 'تم إعطاء $' .. amount .. ' للاعب')
            TriggerClientEvent('chatMessage', targetId, 'SYSTEM', {0, 255, 0}, 'تم إعطاؤك $' .. amount)
            
            -- Update client data
            TriggerEvent('cb_pausemenu:getPlayerData', targetId)
        end
    else
        TriggerClientEvent('chatMessage', source, 'SYSTEM', {255, 0, 0}, 'ليس لديك صلاحية')
    end
end)

-- Event: Admin kick player
RegisterServerEvent('cb_pausemenu:kickPlayer')
AddEventHandler('cb_pausemenu:kickPlayer', function(targetId, reason)
    local source = source
    
    if IsPlayerAdmin(source) then
        local targetName = GetPlayerName(targetId)
        if targetName then
            DropPlayer(targetId, 'تم طردك من السيرفر. السبب: ' .. (reason or 'غير محدد'))
            TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 0}, 'تم طرد اللاعب: ' .. targetName)
        end
    else
        TriggerClientEvent('chatMessage', source, 'SYSTEM', {255, 0, 0}, 'ليس لديك صلاحية')
    end
end)

-- Event: Get online players
RegisterServerEvent('cb_pausemenu:getOnlinePlayers')
AddEventHandler('cb_pausemenu:getOnlinePlayers', function()
    local source = source
    local players = {}
    
    for _, playerId in ipairs(GetPlayers()) do
        local name = GetPlayerName(playerId)
        if name then
            table.insert(players, {
                id = playerId,
                name = name,
                ping = GetPlayerPing(playerId)
            })
        end
    end
    
    TriggerClientEvent('cb_pausemenu:updatePlayerList', source, players)
end)

-- Function to check if player is admin
function IsPlayerAdmin(source)
    local identifier = GetPlayerIdentifier(source, 0)
    
    -- Check against admin list in config
    if Config.Admins then
        for _, admin in pairs(Config.Admins) do
            if identifier == admin then
                return true
            end
        end
    end
    
    return false
end

-- Function to get online players
function GetOnlinePlayers()
    local players = {}
    
    for _, playerId in ipairs(GetPlayers()) do
        local name = GetPlayerName(playerId)
        if name then
            table.insert(players, {
                id = playerId,
                name = name,
                ping = GetPlayerPing(playerId)
            })
        end
    end
    
    return players
end

-- Periodic playtime update
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(60000) -- Update every minute
        
        for source, data in pairs(playerPlaytime) do
            if GetPlayerName(source) then
                local identifier = data.identifier
                local sessionTime = os.time() - data.joinTime
                
                if playerData[identifier] then
                    local totalPlaytime = playerData[identifier].playtime + (sessionTime / 3600)
                    
                    -- Send updated data to client
                    TriggerClientEvent('cb_pausemenu:updatePlayerData', source, {
                        playtime = totalPlaytime,
                        money = playerData[identifier].money or 5000,
                        bank = playerData[identifier].bank or 25000,
                        job = playerData[identifier].job or {name = "unemployed", label = "عاطل"},
                        playerData = playerData[identifier]
                    })
                end
            end
        end
    end
end)

-- Admin commands
RegisterCommand('cbadmin', function(source, args, rawCommand)
    if IsPlayerAdmin(source) then
        TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 255}, 'أوامر الإدارة متاحة في قائمة ESC')
    else
        TriggerClientEvent('chatMessage', source, 'SYSTEM', {255, 0, 0}, 'ليس لديك صلاحية')
    end
end, false)

-- Test command to set player money
RegisterCommand('setmoney', function(source, args, rawCommand)
    if IsPlayerAdmin(source) then
        local targetId = tonumber(args[1])
        local amount = tonumber(args[2]) or 5000
        
        if targetId and GetPlayerName(targetId) then
            local identifier = GetPlayerIdentifier(targetId, 0)
            if playerData[identifier] then
                playerData[identifier].money = amount
                TriggerClientEvent('chatMessage', source, 'ADMIN', {0, 255, 0}, 'تم تعديل مال اللاعب إلى $' .. amount)
                TriggerClientEvent('chatMessage', targetId, 'SYSTEM', {0, 255, 0}, 'تم تعديل مالك إلى $' .. amount)
            end
        end
    end
end, true)

print("^2[CB_PAUSEMENU]^7 Simple server script loaded!")
