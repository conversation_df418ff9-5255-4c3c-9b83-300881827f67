# CB Pause Menu - vRP Integration Guide

دليل التكامل مع نظام vRP لسكربت CB Pause Menu

## متطلبات النظام

### الإصدارات المدعومة:
- ✅ vRP 1.0 (Legacy)
- ✅ vRP 2.0+
- ✅ vRPex
- ✅ vRP Framework المخصص

### المتطلبات الأساسية:
- FiveM Server
- vRP Framework
- MySQL Database
- mysql-async أو oxmysql

## التثبيت والإعداد

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف database.sql في قاعدة البيانات
mysql -u username -p database_name < database.sql
```

### 2. تكوين الإعدادات
```lua
-- في config.lua
Config.Framework = 'vrp'
Config.vRP = {
    useOldVersion = false, -- true للإصدارات القديمة من vRP
    identifierType = 'steam', -- نوع المعرف
    moneyFormat = 'wallet', -- تنسيق المال
    enableGroups = true,
    enableApartments = true,
    enableVehicles = true
}
```

### 3. إضافة الموارد
```lua
-- في server.cfg
ensure vrp
ensure mysql-async
ensure cb_pausemenu
```

## المميزات المتوفرة مع vRP

### 💰 نظام المال
- عرض المال النقدي والبنكي
- إعطاء/سحب المال (للإدارة)
- تتبع المعاملات المالية

### 👤 معلومات اللاعب
- معرف المستخدم (user_id)
- المجموعات والصلاحيات
- الوظائف والرتب
- وقت اللعب والإحصائيات

### 🎒 نظام الحقيبة
- عرض محتويات الحقيبة
- إعطاء/سحب العناصر (للإدارة)
- معلومات الوزن والسعة

### 🚗 المركبات
- قائمة مركبات اللاعب
- حالة المركبات (مخزنة/مستخدمة)
- معلومات الجراج

### 🏠 المنازل والشقق
- قائمة منازل اللاعب
- مواقع المنازل
- معلومات الملكية

### 🛡️ أدوات الإدارة
- نظام الصلاحيات المتقدم
- أوامر الإدارة المخصصة
- سجل الأعمال والتقارير

## الأوامر والوظائف

### أوامر اللاعبين:
```lua
/pausemenu -- فتح قائمة ESC
```

### أوامر الإدارة:
```lua
-- يتم الوصول إليها من خلال القائمة
- إعطاء/سحب المال
- إعطاء/سحب العناصر
- النقل والجلب
- التجميد والشفاء
- الحظر والطرد
```

## التخصيص والإعدادات

### تخصيص المجموعات:
```lua
-- في vrp/vrp_integration.lua
function vRPIntegration.hasPermission(source, permission)
    local user_id = vRP.getUserId({source})
    if user_id then
        return vRP.hasPermission({user_id, permission})
    end
    return false
end
```

### تخصيص الوظائف:
```lua
-- تعديل دالة getPlayerJob حسب نظام الوظائف لديك
function vRPIntegration.getPlayerJob(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local job = vRP.getUserGroupByType({user_id, "job"}) or "unemployed"
        return {
            name = job,
            label = job:gsub("^%l", string.upper),
            grade = 0
        }
    end
end
```

### تخصيص نظام VIP:
```lua
-- في server/functions.lua
function GetPlayerVIPStatus(source)
    local user_id = vRP.getUserId({source})
    if user_id then
        local isVIP = vRP.hasGroup({user_id, "vip"}) or 
                     vRP.hasGroup({user_id, "premium"})
        return {
            isVIP = isVIP,
            level = isVIP and 1 or 0,
            expiry = 0
        }
    end
end
```

## التكامل مع الموارد الأخرى

### التكامل مع vRP_inventory:
```lua
-- إضافة دعم للحقيبة المخصصة
function GetCustomInventory(user_id)
    -- كود مخصص للحقيبة
end
```

### التكامل مع vRP_garages:
```lua
-- إضافة دعم للجراجات المخصصة
function GetCustomVehicles(user_id)
    -- كود مخصص للمركبات
end
```

### التكامل مع vRP_homes:
```lua
-- إضافة دعم للمنازل المخصصة
function GetCustomHomes(user_id)
    -- كود مخصص للمنازل
end
```

## استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة:

**1. القائمة لا تفتح:**
```lua
-- تأكد من تحميل vRP بشكل صحيح
-- تحقق من وجود أخطاء في console
-- تأكد من صحة إعدادات config.lua
```

**2. البيانات لا تظهر:**
```lua
-- تحقق من اتصال قاعدة البيانات
-- تأكد من تشغيل ملف database.sql
-- تحقق من صلاحيات المستخدم في قاعدة البيانات
```

**3. أخطاء vRP:**
```lua
-- تأكد من إصدار vRP المستخدم
-- تحقق من ملف vrp_integration.lua
-- تأكد من تحميل الموارد بالترتيب الصحيح
```

### رسائل الخطأ الشائعة:

```lua
-- "attempt to call a nil value (field 'getUserId')"
-- الحل: تأكد من تحميل vRP قبل cb_pausemenu

-- "MySQL connection failed"
-- الحل: تحقق من إعدادات قاعدة البيانات

-- "Permission denied"
-- الحل: تحقق من صلاحيات المجموعات في vRP
```

## الأداء والتحسين

### نصائح للأداء:
1. استخدم mysql-async للاستعلامات
2. قم بتخزين البيانات مؤقتاً عند الحاجة
3. تجنب الاستعلامات المتكررة
4. استخدم الأحداث بدلاً من الحلقات

### مراقبة الأداء:
```lua
-- تفعيل وضع التطوير للمراقبة
Config.Debug = true
Config.vRP.enableProfiling = true
```

## الدعم والمساعدة

### الحصول على المساعدة:
- Discord: [رابط السيرفر]
- GitHub Issues: [رابط المشاكل]
- الوثائق: [رابط الوثائق]

### الإبلاغ عن الأخطاء:
عند الإبلاغ عن خطأ، يرجى تضمين:
- إصدار vRP المستخدم
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة
- ملف server.log

## التحديثات والصيانة

### التحديث من ESX إلى vRP:
1. نسخ احتياطي من قاعدة البيانات
2. تحديث config.lua
3. تشغيل سكريبت التحويل
4. اختبار جميع الوظائف

### الصيانة الدورية:
- تنظيف سجلات قديمة
- تحديث قاعدة البيانات
- مراجعة الأداء
- تحديث الموارد

---

**CB STORE** - دعم متكامل لنظام vRP 🚀
