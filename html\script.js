// CB Pause Menu JavaScript
let isMenuOpen = false;
let currentSection = 'player';
let playerData = {};
let fpsChart = null;

// Initialize menu when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeMenu();
    setupEventListeners();
});

// Initialize menu components
function initializeMenu() {
    // Hide menu initially
    document.getElementById('pauseMenu').classList.add('hidden');
    
    // Setup navigation
    setupNavigation();
    
    // Initialize FPS chart
    initializeFPSChart();
}

// Setup event listeners
function setupEventListeners() {
    // Navigation items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            switchSection(section);
        });
    });
    
    // Settings inputs
    document.getElementById('showFPS').addEventListener('change', function() {
        toggleFPS(this.checked);
    });
    
    document.getElementById('showMinimap').addEventListener('change', function() {
        toggleMinimap(this.checked);
    });
    
    document.getElementById('showHUD').addEventListener('change', function() {
        toggleHUD(this.checked);
    });
    
    // Volume controls
    document.getElementById('masterVolume').addEventListener('input', function() {
        setVolume('master', this.value);
    });
    
    document.getElementById('musicVolume').addEventListener('input', function() {
        setVolume('music', this.value);
    });
    
    document.getElementById('sfxVolume').addEventListener('input', function() {
        setVolume('sfx', this.value);
    });
    
    // Keyboard events
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isMenuOpen) {
            closeMenu();
        }
    });
}

// Setup navigation
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
        });
    });
}

// Switch between sections
function switchSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;
    }
}

// Initialize FPS Chart
function initializeFPSChart() {
    const ctx = document.getElementById('fpsChart');
    if (ctx) {
        fpsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'FPS',
                    data: [],
                    borderColor: '#00d4ff',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 120,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ccc'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ccc'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}

// NUI Message Handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'openMenu':
            openMenu(data.data);
            break;
        case 'closeMenu':
            closeMenu();
            break;
        case 'updateFPS':
            updateFPS(data.fps);
            break;
        case 'updatePlayerData':
            updatePlayerData(data.data);
            break;
        case 'updateMoney':
            updateMoney(data.money, data.bank);
            break;
        case 'updateJob':
            updateJob(data.job);
            break;
        case 'updateServerPerformance':
            updateServerPerformance(data.performance);
            break;
        case 'updateDiscordAvatar':
            updateDiscordAvatar(data.avatar);
            break;
        case 'serverMessage':
            showNotification(data.message, data.messageType);
            break;
    }
});

// Open menu
function openMenu(data) {
    isMenuOpen = true;
    playerData = data;
    
    // Show menu
    document.getElementById('pauseMenu').classList.remove('hidden');
    
    // Update player info
    updatePlayerInfo(data);
    
    // Update server info
    updateServerInfo(data);
    
    // Focus on menu
    document.body.style.overflow = 'hidden';
}

// Close menu
function closeMenu() {
    isMenuOpen = false;
    
    // Hide menu
    document.getElementById('pauseMenu').classList.add('hidden');
    
    // Restore body scroll
    document.body.style.overflow = 'auto';
    
    // Send close event to client
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
}

// Update player information
function updatePlayerInfo(data) {
    document.getElementById('playerName').textContent = data.playerName || 'غير محدد';
    document.getElementById('playerId').textContent = data.playerId || '0';
    document.getElementById('totalPlaytime').textContent = data.playtime || '0 ساعة';
    document.getElementById('playtime').textContent = data.playtime || '0 ساعة';
    document.getElementById('currentRank').textContent = data.rank?.name || 'مبتدئ';
    document.getElementById('playerRank').textContent = data.rank?.name || 'مبتدئ';
    document.getElementById('cashMoney').textContent = formatMoney(data.money || 0);
    document.getElementById('bankMoney').textContent = formatMoney(data.bank || 0);
    document.getElementById('playerMoney').textContent = formatMoney(data.money || 0);
    document.getElementById('playerJob').textContent = data.job?.label || 'عاطل';
    
    // Update coordinates
    if (data.coords) {
        document.getElementById('coordX').textContent = Math.floor(data.coords.x);
        document.getElementById('coordY').textContent = Math.floor(data.coords.y);
        document.getElementById('coordZ').textContent = Math.floor(data.coords.z);
    }
    
    // Update rank progress
    updateRankProgress(data.rank);
    
    // Update Discord avatar
    if (data.discordAvatar) {
        document.getElementById('playerAvatar').src = data.discordAvatar;
    }
}

// Update server information
function updateServerInfo(data) {
    document.getElementById('serverName').textContent = data.serverName || 'CB STORE SERVER';
    document.getElementById('onlinePlayers').textContent = data.currentPlayers || '0';
    document.getElementById('maxPlayers').textContent = data.maxPlayers || '64';
    document.getElementById('serverPlayers').textContent = `${data.currentPlayers || 0}/${data.maxPlayers || 64}`;
    
    // Update server logo
    if (data.config && data.config.ServerLogo) {
        document.getElementById('serverLogo').src = data.config.ServerLogo;
    }
}

// Update FPS display and chart
function updateFPS(fps) {
    document.getElementById('currentFPS').textContent = fps;
    
    // Update FPS chart
    if (fpsChart) {
        const now = new Date().toLocaleTimeString();
        fpsChart.data.labels.push(now);
        fpsChart.data.datasets[0].data.push(fps);
        
        // Keep only last 20 data points
        if (fpsChart.data.labels.length > 20) {
            fpsChart.data.labels.shift();
            fpsChart.data.datasets[0].data.shift();
        }
        
        fpsChart.update('none');
    }
}

// Update player data
function updatePlayerData(data) {
    if (data.playtime) {
        document.getElementById('totalPlaytime').textContent = data.playtime;
        document.getElementById('playtime').textContent = data.playtime;
    }
    
    if (data.rank) {
        document.getElementById('currentRank').textContent = data.rank.name;
        document.getElementById('playerRank').textContent = data.rank.name;
        updateRankProgress(data.rank);
    }
}

// Update money display
function updateMoney(money, bank) {
    document.getElementById('cashMoney').textContent = formatMoney(money);
    document.getElementById('bankMoney').textContent = formatMoney(bank);
    document.getElementById('playerMoney').textContent = formatMoney(money);
}

// Update job display
function updateJob(job) {
    document.getElementById('playerJob').textContent = job.label || 'عاطل';
}

// Update server performance
function updateServerPerformance(performance) {
    if (performance.players !== undefined) {
        document.getElementById('onlinePlayers').textContent = performance.players;
        document.getElementById('serverPlayers').textContent = `${performance.players}/${performance.maxPlayers || 64}`;
    }
    
    if (performance.uptime) {
        const hours = Math.floor(performance.uptime / 3600000);
        const minutes = Math.floor((performance.uptime % 3600000) / 60000);
        document.getElementById('serverUptime').textContent = `${hours}h ${minutes}m`;
    }
}

// Update Discord avatar
function updateDiscordAvatar(avatar) {
    document.getElementById('playerAvatar').src = avatar;
}

// Update rank progress
function updateRankProgress(rank) {
    if (rank && rank.progress !== undefined) {
        const progressBar = document.getElementById('rankProgress');
        if (progressBar) {
            progressBar.style.width = rank.progress + '%';
        }
    }
}

// Format money with commas
function formatMoney(amount) {
    return '$' + amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// Toggle FPS display
function toggleFPS(show) {
    // Send to client
    fetch(`https://${GetParentResourceName()}/toggleFPS`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({show: show})
    });
}

// Toggle minimap
function toggleMinimap(show) {
    fetch(`https://${GetParentResourceName()}/toggleMinimap`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({show: show})
    });
}

// Toggle HUD
function toggleHUD(show) {
    fetch(`https://${GetParentResourceName()}/toggleHUD`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({show: show})
    });
}

// Set volume
function setVolume(type, value) {
    fetch(`https://${GetParentResourceName()}/setVolume`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({type: type, value: value})
    });
}

// Take screenshot
function takeScreenshot() {
    fetch(`https://${GetParentResourceName()}/takeScreenshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
    
    showNotification('تم أخذ لقطة الشاشة', 'success');
}

// Disconnect player
function disconnectPlayer() {
    if (confirm('هل أنت متأكد من قطع الاتصال؟')) {
        fetch(`https://${GetParentResourceName()}/disconnect`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        });
    }
}

// Teleport to waypoint
function teleportToWaypoint() {
    fetch(`https://${GetParentResourceName()}/teleportToWaypoint`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.getElementById('notifications').appendChild(notification);
    
    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Get parent resource name
function GetParentResourceName() {
    return window.location.hostname;
}
