/*! jQuery v3.6.0 | (c) OpenJS Foundation and other contributors | jquery.org/license */
/* This is a placeholder for jQuery - you should include the actual jQuery library */
/* Download from: https://jquery.com/download/ */

// Minimal jQuery-like functionality for basic operations
(function(global) {
    'use strict';
    
    function $(selector) {
        if (typeof selector === 'string') {
            return new ElementCollection(document.querySelectorAll(selector));
        } else if (selector instanceof Element) {
            return new ElementCollection([selector]);
        } else if (typeof selector === 'function') {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', selector);
            } else {
                selector();
            }
            return;
        }
        return new ElementCollection([]);
    }
    
    function ElementCollection(elements) {
        this.elements = Array.from(elements);
        this.length = this.elements.length;
        
        for (let i = 0; i < this.length; i++) {
            this[i] = this.elements[i];
        }
    }
    
    ElementCollection.prototype = {
        each: function(callback) {
            this.elements.forEach((element, index) => {
                callback.call(element, index, element);
            });
            return this;
        },
        
        on: function(event, handler) {
            this.elements.forEach(element => {
                element.addEventListener(event, handler);
            });
            return this;
        },
        
        off: function(event, handler) {
            this.elements.forEach(element => {
                element.removeEventListener(event, handler);
            });
            return this;
        },
        
        addClass: function(className) {
            this.elements.forEach(element => {
                element.classList.add(className);
            });
            return this;
        },
        
        removeClass: function(className) {
            this.elements.forEach(element => {
                element.classList.remove(className);
            });
            return this;
        },
        
        toggleClass: function(className) {
            this.elements.forEach(element => {
                element.classList.toggle(className);
            });
            return this;
        },
        
        hasClass: function(className) {
            return this.elements.some(element => element.classList.contains(className));
        },
        
        text: function(value) {
            if (value === undefined) {
                return this.elements[0] ? this.elements[0].textContent : '';
            }
            this.elements.forEach(element => {
                element.textContent = value;
            });
            return this;
        },
        
        html: function(value) {
            if (value === undefined) {
                return this.elements[0] ? this.elements[0].innerHTML : '';
            }
            this.elements.forEach(element => {
                element.innerHTML = value;
            });
            return this;
        },
        
        val: function(value) {
            if (value === undefined) {
                return this.elements[0] ? this.elements[0].value : '';
            }
            this.elements.forEach(element => {
                element.value = value;
            });
            return this;
        },
        
        attr: function(name, value) {
            if (value === undefined) {
                return this.elements[0] ? this.elements[0].getAttribute(name) : null;
            }
            this.elements.forEach(element => {
                element.setAttribute(name, value);
            });
            return this;
        },
        
        css: function(property, value) {
            if (typeof property === 'object') {
                this.elements.forEach(element => {
                    Object.assign(element.style, property);
                });
            } else if (value === undefined) {
                return this.elements[0] ? getComputedStyle(this.elements[0])[property] : null;
            } else {
                this.elements.forEach(element => {
                    element.style[property] = value;
                });
            }
            return this;
        },
        
        show: function() {
            this.elements.forEach(element => {
                element.style.display = '';
            });
            return this;
        },
        
        hide: function() {
            this.elements.forEach(element => {
                element.style.display = 'none';
            });
            return this;
        },
        
        fadeIn: function(duration = 300) {
            this.elements.forEach(element => {
                element.style.opacity = '0';
                element.style.display = '';
                element.style.transition = `opacity ${duration}ms`;
                
                setTimeout(() => {
                    element.style.opacity = '1';
                }, 10);
            });
            return this;
        },
        
        fadeOut: function(duration = 300) {
            this.elements.forEach(element => {
                element.style.transition = `opacity ${duration}ms`;
                element.style.opacity = '0';
                
                setTimeout(() => {
                    element.style.display = 'none';
                }, duration);
            });
            return this;
        },
        
        slideUp: function(duration = 300) {
            this.elements.forEach(element => {
                element.style.transition = `height ${duration}ms, padding ${duration}ms, margin ${duration}ms`;
                element.style.height = '0';
                element.style.paddingTop = '0';
                element.style.paddingBottom = '0';
                element.style.marginTop = '0';
                element.style.marginBottom = '0';
                element.style.overflow = 'hidden';
                
                setTimeout(() => {
                    element.style.display = 'none';
                }, duration);
            });
            return this;
        },
        
        slideDown: function(duration = 300) {
            this.elements.forEach(element => {
                element.style.display = '';
                const height = element.scrollHeight;
                element.style.height = '0';
                element.style.overflow = 'hidden';
                element.style.transition = `height ${duration}ms`;
                
                setTimeout(() => {
                    element.style.height = height + 'px';
                }, 10);
                
                setTimeout(() => {
                    element.style.height = '';
                    element.style.overflow = '';
                    element.style.transition = '';
                }, duration);
            });
            return this;
        },
        
        append: function(content) {
            this.elements.forEach(element => {
                if (typeof content === 'string') {
                    element.insertAdjacentHTML('beforeend', content);
                } else {
                    element.appendChild(content);
                }
            });
            return this;
        },
        
        prepend: function(content) {
            this.elements.forEach(element => {
                if (typeof content === 'string') {
                    element.insertAdjacentHTML('afterbegin', content);
                } else {
                    element.insertBefore(content, element.firstChild);
                }
            });
            return this;
        },
        
        remove: function() {
            this.elements.forEach(element => {
                element.remove();
            });
            return this;
        },
        
        find: function(selector) {
            const found = [];
            this.elements.forEach(element => {
                found.push(...element.querySelectorAll(selector));
            });
            return new ElementCollection(found);
        },
        
        parent: function() {
            const parents = this.elements.map(element => element.parentElement).filter(Boolean);
            return new ElementCollection(parents);
        },
        
        children: function() {
            const children = [];
            this.elements.forEach(element => {
                children.push(...element.children);
            });
            return new ElementCollection(children);
        }
    };
    
    // AJAX functionality
    $.ajax = function(options) {
        const defaults = {
            method: 'GET',
            url: '',
            data: null,
            headers: {},
            success: function() {},
            error: function() {}
        };
        
        const settings = Object.assign(defaults, options);
        
        return fetch(settings.url, {
            method: settings.method,
            headers: settings.headers,
            body: settings.data
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => settings.success(data))
        .catch(error => settings.error(error));
    };
    
    $.get = function(url, success) {
        return $.ajax({
            url: url,
            method: 'GET',
            success: success
        });
    };
    
    $.post = function(url, data, success) {
        return $.ajax({
            url: url,
            method: 'POST',
            data: data,
            success: success
        });
    };
    
    // Export to global
    global.$ = global.jQuery = $;
    
})(window);
