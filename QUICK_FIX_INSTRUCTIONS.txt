CB PAUSE MENU - QUICK FIX FOR BLURRED SCREEN
==============================================

PROBLEM: <PERSON><PERSON> shows blurred screen and can't exit

SOLUTION: Use the fixed simple files

QUICK STEPS:
1. Run EMERGENCY_FIX.bat
2. Add your Steam ID to config.lua
3. Restart server
4. Test with ESC key

MANUAL STEPS:
1. Copy fxmanifest_simple.lua to fxmanifest.lua
2. Copy client_simple.lua to client.lua  
3. Copy server_simple.lua to server.lua
4. Copy config_simple.lua to config.lua
5. Copy html/index_simple.html to html/index.html

EMERGENCY COMMANDS (if stuck in menu):
- /forceclosemenu
- /closemenu  
- Press F1 key

TEST COMMANDS:
- /pausemenu (open menu)
- /addplaytime 10 (add 10 hours)
- /addmoney 5000 (add $5000)
- /cbcheck (check for errors)

STEAM ID SETUP:
1. Press F8 in game
2. Type: print(GetPlayerIdentifier(PlayerId(), 0))
3. Copy the result (steam:xxxxxxxxxx)
4. Add to config.lua in Config.Admins section

WHAT'S FIXED:
- Simplified HTML (no external libraries)
- Fixed NUI focus issues
- Added emergency close options
- Removed problematic control disabling
- Clean, working interface

WHAT TO EXPECT:
- Clean, simple menu interface
- Easy open/close with ESC
- All player info displayed
- Working buttons
- No more blurred screen

IF STILL NOT WORKING:
1. Check F8 console for errors
2. Make sure all files are copied correctly
3. Verify Steam ID is correct in config.lua
4. Try /refresh then /ensure cb_pausemenu

SUPPORT:
- Check EMERGENCY_FIX.md for detailed instructions
- Check TROUBLESHOOTING.md for common issues
- Use /cbcheck command for diagnostics

CB STORE - Fixed and Ready to Use!
