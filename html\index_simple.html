<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CB Pause Menu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: transparent;
            color: white;
            overflow: hidden;
            margin: 0;
            padding: 0;
            user-select: none;
        }

        .pause-menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .pause-menu.active {
            display: flex;
        }

        .menu-container {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 20px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .menu-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #00d4ff;
            padding-bottom: 20px;
        }

        .menu-header h1 {
            color: #00d4ff;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 0, 0, 1);
            transform: scale(1.1);
        }

        .player-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .info-card h3 {
            color: #00d4ff;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #ccc;
        }

        .info-value {
            color: #fff;
            font-weight: bold;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .action-btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        .action-btn.danger {
            background: linear-gradient(45deg, #ff4757, #ff3742);
        }

        .action-btn.danger:hover {
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
        }

        .fps-display {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #00d4ff;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #0099cc;
        }
    </style>
</head>
<body>
    <!-- FPS Display -->
    <div class="fps-display" id="fpsDisplay">FPS: 60</div>

    <!-- Pause Menu -->
    <div class="pause-menu" id="pauseMenu">
        <div class="menu-container">
            <button class="close-btn" onclick="closeMenu()">&times;</button>
            
            <div class="menu-header">
                <h1>CB STORE SERVER</h1>
                <p>قائمة الإيقاف المؤقت</p>
            </div>

            <div class="player-info">
                <div class="info-card">
                    <h3>معلومات اللاعب</h3>
                    <div class="info-item">
                        <span class="info-label">اسم اللاعب:</span>
                        <span class="info-value" id="playerName">غير محدد</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">معرف اللاعب:</span>
                        <span class="info-value" id="playerId">0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">وقت اللعب:</span>
                        <span class="info-value" id="playtime">0 ساعة</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الرتبة:</span>
                        <span class="info-value" id="playerRank">مبتدئ</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>المعلومات المالية</h3>
                    <div class="info-item">
                        <span class="info-label">المال النقدي:</span>
                        <span class="info-value" id="playerMoney">$0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المال البنكي:</span>
                        <span class="info-value" id="bankMoney">$0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الوظيفة:</span>
                        <span class="info-value" id="playerJob">عاطل</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>الموقع الحالي</h3>
                    <div class="info-item">
                        <span class="info-label">الإحداثيات:</span>
                        <span class="info-value" id="playerCoords">0, 0, 0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">معدل الإطارات:</span>
                        <span class="info-value" id="currentFPS">60 FPS</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>معلومات السيرفر</h3>
                    <div class="info-item">
                        <span class="info-label">اللاعبين المتصلين:</span>
                        <span class="info-value" id="serverPlayers">0/64</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">اسم السيرفر:</span>
                        <span class="info-value" id="serverName">CB STORE SERVER</span>
                    </div>
                </div>
            </div>

            <div class="actions">
                <button class="action-btn" onclick="takeScreenshot()">لقطة شاشة</button>
                <button class="action-btn" onclick="teleportToWaypoint()">النقل للنقطة</button>
                <button class="action-btn danger" onclick="disconnectPlayer()">قطع الاتصال</button>
                <button class="action-btn" onclick="closeMenu()">إغلاق</button>
            </div>
        </div>
    </div>

    <script>
        let isMenuOpen = false;

        // Handle messages from client
        window.addEventListener('message', function(event) {
            const data = event.data;
            
            switch(data.type) {
                case 'openMenu':
                    openMenu(data.data);
                    break;
                case 'closeMenu':
                    closeMenu();
                    break;
                case 'updateFPS':
                    updateFPS(data.fps);
                    break;
            }
        });

        function openMenu(data) {
            console.log('Opening menu with data:', data);
            
            isMenuOpen = true;
            document.getElementById('pauseMenu').classList.add('active');
            
            // Update player info
            if (data) {
                document.getElementById('playerName').textContent = data.playerName || 'غير محدد';
                document.getElementById('playerId').textContent = data.playerId || '0';
                document.getElementById('playtime').textContent = data.playtime || '0 ساعة';
                document.getElementById('playerRank').textContent = data.rank?.name || 'مبتدئ';
                document.getElementById('playerMoney').textContent = '$' + (data.money || 0);
                document.getElementById('bankMoney').textContent = '$' + (data.bank || 0);
                document.getElementById('playerJob').textContent = data.job?.label || 'عاطل';
                document.getElementById('currentFPS').textContent = (data.fps || 60) + ' FPS';
                document.getElementById('serverName').textContent = data.serverName || 'CB STORE SERVER';
                document.getElementById('serverPlayers').textContent = (data.currentPlayers || 0) + '/' + (data.maxPlayers || 64);
                
                if (data.coords) {
                    document.getElementById('playerCoords').textContent = 
                        data.coords.x + ', ' + data.coords.y + ', ' + data.coords.z;
                }
            }
        }

        function closeMenu() {
            console.log('Closing menu');

            isMenuOpen = false;
            document.getElementById('pauseMenu').classList.remove('active');

            // Send close message to client immediately
            try {
                fetch(`https://${GetParentResourceName()}/closeMenu`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
            } catch (e) {
                console.log('Error sending close message:', e);
            }
        }

        function updateFPS(fps) {
            document.getElementById('fpsDisplay').textContent = 'FPS: ' + fps;
            document.getElementById('currentFPS').textContent = fps + ' FPS';
        }

        function takeScreenshot() {
            fetch(`https://${GetParentResourceName()}/takeScreenshot`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            }).catch(() => {});
        }

        function teleportToWaypoint() {
            fetch(`https://${GetParentResourceName()}/teleportToWaypoint`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            }).catch(() => {});
        }

        function disconnectPlayer() {
            if (confirm('هل أنت متأكد من قطع الاتصال؟')) {
                fetch(`https://${GetParentResourceName()}/disconnect`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                }).catch(() => {});
            }
        }

        function GetParentResourceName() {
            return window.location.hostname || 'cb_pausemenu';
        }

        // Handle keyboard events
        document.addEventListener('keydown', function(e) {
            if (isMenuOpen) {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    closeMenu();
                }
                // Prevent other keys from affecting game
                e.stopPropagation();
            }
        });

        // Handle mouse events
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Prevent text selection
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
        });

        // Handle window focus
        window.addEventListener('focus', function() {
            if (isMenuOpen) {
                console.log('Window focused while menu open');
            }
        });

        window.addEventListener('blur', function() {
            if (isMenuOpen) {
                console.log('Window blurred while menu open');
            }
        });

        console.log('CB Pause Menu HTML loaded');
    </script>
</body>
</html>
