{"name": "cb_pausemenu", "version": "1.0.0", "description": "Advanced ESC Menu System for FiveM with Arabic support", "main": "fxmanifest.lua", "scripts": {"dev": "npm run watch", "build": "npm run build:css && npm run build:js", "build:css": "postcss html/style.css -o html/style.min.css --map", "build:js": "terser html/script.js -o html/script.min.js --source-map", "watch": "npm-run-all --parallel watch:*", "watch:css": "postcss html/style.css -o html/style.min.css --map --watch", "watch:js": "terser html/script.js -o html/script.min.js --source-map --watch", "lint": "npm run lint:js && npm run lint:css", "lint:js": "eslint html/script.js lib/*.js", "lint:css": "stylelint html/style.css lib/*.css", "format": "prettier --write \"**/*.{js,css,html,md,json}\"", "test": "npm run lint", "clean": "rimraf html/*.min.* lib/*.min.*", "optimize": "npm run clean && npm run build && npm run optimize:images", "optimize:images": "imagemin html/assets/*.{jpg,png,svg} --out-dir=html/assets/optimized", "validate": "npm run validate:lua && npm run validate:sql", "validate:lua": "luacheck *.lua client/*.lua server/*.lua lib/*.lua", "validate:sql": "sqlfluff lint database.sql", "docs": "jsdoc html/script.js lib/*.js -d docs", "release": "npm run test && npm run build && npm run docs"}, "keywords": ["fivem", "esx", "qbcore", "pause-menu", "esc-menu", "arabic", "roleplay", "gaming", "lua", "javascript", "html", "css"], "author": "CB STORE", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cb-store/cb_pausemenu.git"}, "bugs": {"url": "https://github.com/cb-store/cb_pausemenu/issues"}, "homepage": "https://github.com/cb-store/cb_pausemenu#readme", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "autoprefixer": "^10.4.0", "cssnano": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.0", "imagemin": "^8.0.0", "imagemin-cli": "^7.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.0", "imagemin-svgo": "^10.0.0", "jsdoc": "^4.0.0", "npm-run-all": "^4.1.0", "postcss": "^8.4.0", "postcss-cli": "^10.1.0", "prettier": "^2.8.0", "rimraf": "^5.0.0", "stylelint": "^15.6.0", "stylelint-config-prettier": "^9.0.0", "stylelint-config-standard": "^33.0.0", "terser": "^5.17.0"}, "dependencies": {"chart.js": "^4.3.0", "jquery": "^3.7.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "eslintConfig": {"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-unused-vars": "warn", "no-console": "warn", "prefer-const": "error"}, "globals": {"GetParentResourceName": "readonly", "fetch": "readonly", "Chart": "readonly", "$": "readonly", "jQuery": "readonly"}}, "stylelint": {"extends": ["stylelint-config-standard", "stylelint-config-prettier"], "rules": {"color-hex-case": "lower", "color-hex-length": "short", "declaration-block-trailing-semicolon": "always", "indentation": 2, "max-empty-lines": 2, "rule-empty-line-before": ["always", {"except": ["first-nested"]}]}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 80, "bracketSpacing": true, "arrowParens": "avoid"}, "postcss": {"plugins": {"autoprefixer": {}, "cssnano": {"preset": "default"}}}, "imagemin": {"plugins": ["imagemin-mozjpeg", "imagemin-pngquant", "imagemin-svgo"]}}